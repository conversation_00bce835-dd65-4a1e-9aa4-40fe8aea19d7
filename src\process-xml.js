import Database from './database.js';
import ClinVarXMLParser from './xml-parser.js';
import path from 'path';

class ClinVarProcessor {
  constructor() {
    this.db = new Database();
    this.parser = new ClinVarXMLParser();
    this.processedCount = 0;
    this.errorCount = 0;
  }

  async initialize() {
    await this.db.initialize();
  }

  async processXMLFile(filePath) {
    console.log(`\n=== Procesando archivo: ${filePath} ===`);
    
    try {
      const parsedData = await this.parser.parseFile(filePath);
      
      // Navegar a la estructura de datos
      const clinvarSet = parsedData.clinvarset;
      if (!clinvarSet) {
        console.log('No se encontró elemento ClinVarSet en el XML');
        return;
      }

      const variationArchives = clinvarSet.variationarchive;
      if (!variationArchives) {
        console.log('No se encontraron VariationArchive en el XML');
        return;
      }

      const archives = Array.isArray(variationArchives) ? variationArchives : [variationArchives];
      console.log(`Encontradas ${archives.length} variantes para procesar`);

      for (let i = 0; i < archives.length; i++) {
        await this.processVariant(archives[i], i + 1, archives.length);
      }

      console.log(`\n=== Procesamiento completado ===`);
      console.log(`Variantes procesadas: ${this.processedCount}`);
      console.log(`Errores: ${this.errorCount}`);

    } catch (error) {
      console.error('Error procesando archivo XML:', error);
      this.errorCount++;
    }
  }

  async processVariant(variationArchive, current, total) {
    try {
      // Extraer datos de la variante
      const variantData = this.parser.extractVariantData(variationArchive);
      if (!variantData || !variantData.vcv_id) {
        console.log(`Variante ${current}/${total}: Sin datos válidos, saltando...`);
        this.errorCount++;
        return;
      }

      console.log(`Procesando variante ${current}/${total}: ${variantData.vcv_id}`);

      // Insertar variante principal
      const variantId = await this.db.insertVariant(variantData);

      // Extraer e insertar genes
      const genes = this.parser.extractGenes(variationArchive);
      for (const gene of genes) {
        await this.db.insertGene(variantId, gene);
      }

      // Extraer e insertar condiciones
      const conditions = this.parser.extractConditions(variationArchive);
      for (const condition of conditions) {
        await this.db.insertCondition(variantId, condition);
      }

      // Extraer e insertar ubicaciones genómicas
      const locations = this.parser.extractGenomicLocations(variationArchive);
      for (const location of locations) {
        await this.db.insertGenomicLocation(variantId, location);
      }

      // Extraer e insertar submitters
      const submitters = this.parser.extractSubmitters(variationArchive);
      for (const submitter of submitters) {
        await this.db.insertSubmitter(variantId, submitter);
      }

      this.processedCount++;

      // Mostrar progreso cada 10 variantes
      if (current % 10 === 0) {
        console.log(`Progreso: ${current}/${total} variantes procesadas`);
      }

    } catch (error) {
      console.error(`Error procesando variante ${current}:`, error);
      this.errorCount++;
    }
  }

  async showStatistics() {
    console.log('\n=== Estadísticas de la base de datos ===');
    
    const variantCount = await this.db.getVariantCount();
    console.log(`Total de variantes en la base de datos: ${variantCount}`);

    if (variantCount > 0) {
      console.log('\n--- Últimas 5 variantes procesadas ---');
      const recentVariants = await this.db.getVariants(5, 0);
      
      recentVariants.forEach((variant, index) => {
        console.log(`${index + 1}. ${variant.vcv_id}: ${variant.title}`);
        console.log(`   Significancia clínica: ${variant.clinical_significance || 'No especificada'}`);
        console.log(`   Última actualización: ${variant.date_last_updated || 'No especificada'}`);
        console.log('');
      });
    }
  }

  async close() {
    this.db.close();
  }
}

// Función principal
async function main() {
  const processor = new ClinVarProcessor();
  
  try {
    await processor.initialize();
    
    // Procesar el archivo XML
    const xmlFile = './part_000.xml';
    await processor.processXMLFile(xmlFile);
    
    // Mostrar estadísticas
    await processor.showStatistics();
    
  } catch (error) {
    console.error('Error en el procesamiento:', error);
  } finally {
    await processor.close();
  }
}

// Ejecutar si es el archivo principal
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export default ClinVarProcessor;
