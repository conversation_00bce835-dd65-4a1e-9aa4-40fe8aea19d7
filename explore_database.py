#!/usr/bin/env python3
"""
Explorador interactivo de la base de datos ClinVar
"""

import sqlite3
import os
from database import ClinVarDatabase

def explore_database_structure(db_path):
    """Explorar la estructura completa de la base de datos"""
    print(f"🔍 Explorando base de datos: {db_path}")
    print("=" * 60)
    
    if not os.path.exists(db_path):
        print(f"❌ Base de datos no encontrada: {db_path}")
        return
    
    db = ClinVarDatabase(db_path)
    db.connect()
    
    try:
        cursor = db.connection.cursor()
        
        # Obtener información de todas las tablas
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name NOT LIKE 'sqlite_%'
            ORDER BY name
        """)
        
        tables = cursor.fetchall()
        print(f"📋 Tablas encontradas: {len(tables)}")
        
        for table in tables:
            table_name = table[0]
            print(f"\n🗂️  Tabla: {table_name}")
            print("-" * 40)
            
            # Obtener esquema de la tabla
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            
            print("📊 Columnas:")
            for col in columns:
                col_id, name, data_type, not_null, default, pk = col
                pk_str = " (PK)" if pk else ""
                not_null_str = " NOT NULL" if not_null else ""
                default_str = f" DEFAULT {default}" if default else ""
                print(f"   - {name}: {data_type}{pk_str}{not_null_str}{default_str}")
            
            # Contar registros
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f"📈 Registros: {count:,}")
            
            # Mostrar algunos ejemplos si hay datos
            if count > 0:
                cursor.execute(f"SELECT * FROM {table_name} LIMIT 3")
                samples = cursor.fetchall()
                
                print("📝 Ejemplos de datos:")
                column_names = [desc[1] for desc in columns]
                
                for i, sample in enumerate(samples, 1):
                    print(f"   Registro {i}:")
                    for j, value in enumerate(sample):
                        if value is not None:
                            value_str = str(value)
                            if len(value_str) > 50:
                                value_str = value_str[:47] + "..."
                            print(f"     {column_names[j]}: {value_str}")
                    print()
        
        # Mostrar algunas consultas interesantes
        show_interesting_queries(cursor)
        
    except Exception as e:
        print(f"❌ Error explorando base de datos: {e}")
    finally:
        db.close()

def show_interesting_queries(cursor):
    """Mostrar consultas interesantes sobre los datos"""
    print("\n🔬 Análisis de datos interesantes")
    print("=" * 40)
    
    try:
        # Distribución por cromosoma
        print("\n📍 Distribución por cromosoma (top 10):")
        cursor.execute("""
            SELECT chromosome, COUNT(*) as count
            FROM genomic_locations
            WHERE chromosome IS NOT NULL
            GROUP BY chromosome
            ORDER BY count DESC
            LIMIT 10
        """)
        
        for row in cursor.fetchall():
            print(f"   Chr{row[0]}: {row[1]:,} ubicaciones")
        
        # Submitters más activos
        print("\n👥 Submitters más activos:")
        cursor.execute("""
            SELECT submitter_name, COUNT(*) as submissions
            FROM submitters
            WHERE submitter_name IS NOT NULL
            GROUP BY submitter_name
            ORDER BY submissions DESC
            LIMIT 10
        """)
        
        for row in cursor.fetchall():
            print(f"   {row[0]}: {row[1]:,} envíos")
        
        # Condiciones más comunes
        print("\n🏥 Condiciones más comunes:")
        cursor.execute("""
            SELECT condition_name, COUNT(*) as count
            FROM conditions
            WHERE condition_name IS NOT NULL
            GROUP BY condition_name
            ORDER BY count DESC
            LIMIT 10
        """)
        
        for row in cursor.fetchall():
            condition = row[0]
            if len(condition) > 50:
                condition = condition[:47] + "..."
            print(f"   {condition}: {row[1]:,}")
        
        # Variantes por año de creación
        print("\n📅 Variantes por año de creación:")
        cursor.execute("""
            SELECT substr(date_created, 1, 4) as year, COUNT(*) as count
            FROM variants
            WHERE date_created IS NOT NULL
            GROUP BY year
            ORDER BY year DESC
            LIMIT 10
        """)
        
        for row in cursor.fetchall():
            print(f"   {row[0]}: {row[1]:,} variantes")
        
        # Estados de revisión
        print("\n📋 Estados de revisión:")
        cursor.execute("""
            SELECT review_status, COUNT(*) as count
            FROM variants
            WHERE review_status IS NOT NULL
            GROUP BY review_status
            ORDER BY count DESC
        """)
        
        for row in cursor.fetchall():
            status = row[0]
            if len(status) > 40:
                status = status[:37] + "..."
            print(f"   {status}: {row[1]:,}")
        
        # Significancia clínica
        print("\n🔬 Significancia clínica:")
        cursor.execute("""
            SELECT clinical_significance, COUNT(*) as count
            FROM variants
            WHERE clinical_significance IS NOT NULL
            GROUP BY clinical_significance
            ORDER BY count DESC
        """)
        
        for row in cursor.fetchall():
            significance = row[0] if row[0] else "No especificada"
            print(f"   {significance}: {row[1]:,}")
        
    except Exception as e:
        print(f"❌ Error en consultas de análisis: {e}")

def show_sample_variants(db_path, limit=5):
    """Mostrar variantes de muestra con todos sus datos relacionados"""
    print(f"\n🧬 Variantes de muestra (primeras {limit})")
    print("=" * 50)
    
    db = ClinVarDatabase(db_path)
    db.connect()
    
    try:
        variants = db.get_variants(limit, 0)
        
        for i, variant in enumerate(variants, 1):
            print(f"\n--- Variante {i}: {variant['vcv_id']} ---")
            print(f"📝 Título: {variant['title']}")
            print(f"🔬 Tipo: {variant['variation_type'] or 'No especificado'}")
            print(f"🏥 Significancia: {variant['clinical_significance'] or 'No especificada'}")
            print(f"📅 Creada: {variant['date_created']}")
            print(f"📅 Actualizada: {variant['date_last_updated']}")
            
            # Obtener detalles completos
            details = db.get_variant_details(variant['vcv_id'])
            if details:
                print(f"🧬 Genes: {len(details['genes'])}")
                if details['genes']:
                    for gene in details['genes'][:2]:  # Mostrar solo los primeros 2
                        print(f"   - {gene['gene_symbol']} ({gene['gene_id']})")
                
                print(f"🏥 Condiciones: {len(details['conditions'])}")
                if details['conditions']:
                    for condition in details['conditions'][:2]:
                        print(f"   - {condition['condition_name'][:50]}...")
                
                print(f"📍 Ubicaciones: {len(details['genomic_locations'])}")
                if details['genomic_locations']:
                    for location in details['genomic_locations'][:2]:
                        if location['chromosome']:
                            print(f"   - Chr{location['chromosome']}")
                
                print(f"👥 Submitters: {len(details['submitters'])}")
                if details['submitters']:
                    for submitter in details['submitters'][:2]:
                        print(f"   - {submitter['submitter_name']}")
    
    except Exception as e:
        print(f"❌ Error mostrando variantes de muestra: {e}")
    finally:
        db.close()

def main():
    """Función principal"""
    print("🧬 Explorador de Base de Datos ClinVar")
    print("=" * 50)
    
    # Verificar qué bases de datos están disponibles
    databases = []
    for db_file in ['clinvar_sample.db', 'clinvar.db']:
        if os.path.exists(db_file):
            size = os.path.getsize(db_file)
            databases.append((db_file, size))
    
    if not databases:
        print("❌ No se encontraron bases de datos")
        print("💡 Ejecuta primero: python process_sample.py")
        return
    
    print("📁 Bases de datos disponibles:")
    for db_file, size in databases:
        print(f"   - {db_file} ({size:,} bytes)")
    
    # Explorar la base de datos principal (la más grande)
    main_db = max(databases, key=lambda x: x[1])[0]
    print(f"\n🎯 Explorando: {main_db}")
    
    # Explorar estructura
    explore_database_structure(main_db)
    
    # Mostrar variantes de muestra
    show_sample_variants(main_db)
    
    print(f"\n✅ Exploración completada")
    print(f"💡 Para consultas específicas usa: python query_clinvar.py")

if __name__ == "__main__":
    main()
