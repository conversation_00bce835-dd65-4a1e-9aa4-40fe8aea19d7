import xml.etree.ElementTree as ET
from typing import Dict, List, Any, Optional
import os

class ClinVarXMLParser:
    def __init__(self):
        self.namespaces = {
            'clinvar': 'http://www.ncbi.nlm.nih.gov/clinvar'
        }
    
    def parse_file(self, file_path: str) -> ET.Element:
        """Parsear archivo XML y retornar el elemento raíz"""
        print(f"📖 Leyendo archivo XML: {file_path}")
        
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"Archivo no encontrado: {file_path}")
        
        try:
            tree = ET.parse(file_path)
            root = tree.getroot()
            print(f"✅ XML parseado correctamente. Elemento raíz: {root.tag}")
            return root
        except ET.ParseError as e:
            raise Exception(f"Error parseando XML: {e}")
    
    def get_text_or_none(self, element: Optional[ET.Element], default: str = None) -> Optional[str]:
        """Obtener texto de un elemento o None si no existe"""
        if element is not None and element.text:
            return element.text.strip()
        return default
    
    def get_attribute_or_none(self, element: Optional[ET.Element], attr: str, default: str = None) -> Optional[str]:
        """Obtener atributo de un elemento o None si no existe"""
        if element is not None:
            return element.get(attr, default)
        return default
    
    def extract_variant_data(self, variation_archive: ET.Element) -> Optional[Dict[str, Any]]:
        """Extraer datos principales de una variante"""
        try:
            # Obtener datos de los atributos del VariationArchive
            vcv_id = self.get_attribute_or_none(variation_archive, 'Accession')
            if not vcv_id:
                return None

            # Información básica desde atributos
            title = self.get_attribute_or_none(variation_archive, 'VariationName', "Sin título")
            variation_type = self.get_attribute_or_none(variation_archive, 'VariationType')

            # Fechas desde atributos
            date_created = self.get_attribute_or_none(variation_archive, 'DateCreated')
            date_last_updated = self.get_attribute_or_none(variation_archive, 'DateLastUpdated')

            # Especie desde elemento hijo
            species_elem = variation_archive.find('.//Species')
            species = self.get_text_or_none(species_elem, "Homo sapiens")

            # Significancia clínica desde ClinicalAssertion
            clinical_assertion = variation_archive.find('.//ClinicalAssertion')
            review_status = None
            clinical_significance = None
            interpretation_description = None

            if clinical_assertion is not None:
                # Buscar ReviewStatus
                review_status_elem = clinical_assertion.find('.//ReviewStatus')
                review_status = self.get_text_or_none(review_status_elem)

                # Buscar Interpretation
                interpretation_elem = clinical_assertion.find('.//Interpretation/Description')
                clinical_significance = self.get_text_or_none(interpretation_elem)

                # Buscar Comment
                comment_elem = clinical_assertion.find('.//Interpretation/Comment')
                interpretation_description = self.get_text_or_none(comment_elem)

            return {
                'vcv_id': vcv_id,
                'title': title,
                'species': species,
                'date_created': date_created,
                'date_last_updated': date_last_updated,
                'review_status': review_status,
                'clinical_significance': clinical_significance,
                'interpretation_description': interpretation_description,
                'variation_type': variation_type
            }

        except Exception as e:
            print(f"❌ Error extrayendo datos de variante: {e}")
            return None
    
    def extract_genes(self, variation_archive: ET.Element) -> List[Dict[str, Any]]:
        """Extraer información de genes"""
        genes = []
        try:
            gene_elements = variation_archive.findall('.//Gene')

            for gene_elem in gene_elements:
                gene_id = self.get_attribute_or_none(gene_elem, 'GeneID')
                gene_symbol = self.get_attribute_or_none(gene_elem, 'Symbol')
                hgnc_id = self.get_attribute_or_none(gene_elem, 'HGNC_ID')
                full_name = self.get_attribute_or_none(gene_elem, 'FullName')

                if gene_id or gene_symbol:
                    genes.append({
                        'gene_id': gene_id,
                        'gene_symbol': gene_symbol,
                        'hgnc_id': hgnc_id,
                        'full_name': full_name
                    })
        except Exception as e:
            print(f"❌ Error extrayendo genes: {e}")

        return genes
    
    def extract_conditions(self, variation_archive: ET.Element) -> List[Dict[str, Any]]:
        """Extraer información de condiciones"""
        conditions = []
        try:
            # Buscar en diferentes ubicaciones posibles
            trait_elements = variation_archive.findall('.//TraitSet/Trait')

            for trait_elem in trait_elements:
                trait_id = self.get_attribute_or_none(trait_elem, 'ID')
                trait_type = self.get_attribute_or_none(trait_elem, 'Type')

                # Buscar nombres de la condición
                name_elements = trait_elem.findall('.//Name/ElementValue')
                if not name_elements:
                    # Buscar directamente en Name
                    name_elements = trait_elem.findall('.//Name')

                for name_elem in name_elements:
                    condition_name = self.get_text_or_none(name_elem)
                    name_type = self.get_attribute_or_none(name_elem, 'Type')

                    if condition_name:
                        conditions.append({
                            'condition_name': condition_name,
                            'condition_id': trait_id,
                            'db_name': name_type or trait_type
                        })

            # Si no encontramos condiciones, buscar en otros lugares
            if not conditions:
                # Buscar en ClinicalAssertion
                clinical_assertions = variation_archive.findall('.//ClinicalAssertion')
                for assertion in clinical_assertions:
                    trait_sets = assertion.findall('.//TraitSet')
                    for trait_set in trait_sets:
                        traits = trait_set.findall('.//Trait')
                        for trait in traits:
                            trait_id = self.get_attribute_or_none(trait, 'ID')
                            names = trait.findall('.//Name')
                            for name in names:
                                condition_name = self.get_text_or_none(name)
                                if condition_name:
                                    conditions.append({
                                        'condition_name': condition_name,
                                        'condition_id': trait_id,
                                        'db_name': 'clinical_assertion'
                                    })

        except Exception as e:
            print(f"❌ Error extrayendo condiciones: {e}")

        return conditions
    
    def extract_genomic_locations(self, variation_archive: ET.Element) -> List[Dict[str, Any]]:
        """Extraer ubicaciones genómicas"""
        locations = []
        try:
            # Buscar en diferentes ubicaciones posibles
            location_elements = variation_archive.findall('.//Location/SequenceLocation')

            # Si no encontramos, buscar directamente SequenceLocation
            if not location_elements:
                location_elements = variation_archive.findall('.//SequenceLocation')

            for loc_elem in location_elements:
                chromosome = self.get_attribute_or_none(loc_elem, 'Chr')
                start_pos = self.get_attribute_or_none(loc_elem, 'start')
                stop_pos = self.get_attribute_or_none(loc_elem, 'stop')
                ref_allele = self.get_attribute_or_none(loc_elem, 'referenceAllele')
                alt_allele = self.get_attribute_or_none(loc_elem, 'alternateAllele')
                assembly = self.get_attribute_or_none(loc_elem, 'Assembly')

                # También buscar otros atributos posibles
                if not start_pos:
                    start_pos = self.get_attribute_or_none(loc_elem, 'Start')
                if not stop_pos:
                    stop_pos = self.get_attribute_or_none(loc_elem, 'Stop')

                locations.append({
                    'chromosome': chromosome,
                    'start_position': int(start_pos) if start_pos and start_pos.isdigit() else None,
                    'stop_position': int(stop_pos) if stop_pos and stop_pos.isdigit() else None,
                    'reference_allele': ref_allele,
                    'alternate_allele': alt_allele,
                    'assembly': assembly
                })
        except Exception as e:
            print(f"❌ Error extrayendo ubicaciones genómicas: {e}")

        return locations
    
    def extract_submitters(self, variation_archive: ET.Element) -> List[Dict[str, Any]]:
        """Extraer información de submitters"""
        submitters = []
        try:
            assertion_elements = variation_archive.findall('.//ClinicalAssertion')

            for assertion_elem in assertion_elements:
                # Buscar información del submitter
                submitter_elem = assertion_elem.find('.//ClinVarSubmissionID')
                submission_date = self.get_attribute_or_none(assertion_elem, 'SubmissionDate')

                submitter_name = None
                if submitter_elem is not None:
                    submitter_name = self.get_attribute_or_none(submitter_elem, 'submitter')

                # También buscar en ClinVarAccession
                if not submitter_name:
                    accession_elem = assertion_elem.find('.//ClinVarAccession')
                    if accession_elem is not None:
                        submitter_name = self.get_attribute_or_none(accession_elem, 'SubmitterName')

                if submitter_name:
                    submitters.append({
                        'submitter_name': submitter_name,
                        'submission_date': submission_date
                    })
        except Exception as e:
            print(f"❌ Error extrayendo submitters: {e}")

        return submitters
    
    def count_variation_archives(self, root: ET.Element) -> int:
        """Contar el número de VariationArchive en el XML"""
        variation_archives = root.findall('.//VariationArchive')
        return len(variation_archives)
    
    def get_variation_archives(self, root: ET.Element) -> List[ET.Element]:
        """Obtener todos los elementos VariationArchive"""
        return root.findall('.//VariationArchive')
