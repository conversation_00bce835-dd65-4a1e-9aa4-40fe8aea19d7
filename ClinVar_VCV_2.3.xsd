<xs:schema attributeFormDefault="unqualified" elementFormDefault="qualified" version="2.3; April 20, 2025"
    xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="ClinVarVariationRelease" type="ReleaseType">
        <xs:annotation>
            <xs:documentation>The element to group each VariationArchive element in the release
            </xs:documentation>
        </xs:annotation>
    </xs:element>
    <xs:element name="ClassifiedRecord">
        <xs:annotation>
            <xs:documentation>This element is restricted to variation records for which an explicit
                classification was submitted. Compare to IncludedRecord, which provides aggregate
                information about variants that are part of another submission, but for which
                ClinVar has *not* received a submission specific to that variant independently. </xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:choice>
                    <xs:element name="SimpleAllele" type="typeAllele">
                        <xs:annotation>
                            <xs:documentation> Describes a single sequence change relative to a
                                contiguous region of a chromosome or the mitochondrion.
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element maxOccurs="1" minOccurs="1" name="Haplotype" type="typeHaplotype">
                        <xs:annotation>
                            <xs:documentation> Describes multiple sequence changes on one of the
                                chromosomes of a homologous pair or on the mitochondrion.
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element maxOccurs="1" minOccurs="1" name="Genotype" type="typeGenotype">
                        <xs:annotation>
                            <xs:documentation>Describes the combination of sequence changes on each
                                chromosome of a homologous pair </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:choice>
                <xs:element maxOccurs="1" minOccurs="1" name="RCVList">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded" minOccurs="1" name="RCVAccession"
                                type="typeRCV">
                                <xs:annotation>
                                    <xs:documentation>Maintains the list of RCV accessions and their
                                        titles referencing the VariationID, in part to
                                        support indexing and retrieval for NCBI's web services. This
                                        structure does not report RCV/SCV
                                        relationships</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                        </xs:sequence>
                        <xs:attribute name="SubmissionCount" type="xs:positiveInteger">
                            <xs:annotation>
                                <xs:documentation>The number of submissions (SCV accessions)
                                    referencing the VariationID </xs:documentation>
                            </xs:annotation>
                        </xs:attribute>
                        <xs:attribute name="IndependentObservations" type="xs:positiveInteger">
                        </xs:attribute>
                    </xs:complexType>
                </xs:element>
                <xs:element minOccurs="1" name="Classifications"
                    type="typeAggregateClassificationSet"/>
                <xs:element maxOccurs="1" minOccurs="1" name="ClinicalAssertionList">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded" minOccurs="1" name="ClinicalAssertion"
                                type="MeasureTraitType"> </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="TraitMappingList">
                    <xs:annotation>
                        <xs:documentation>This element is used to report how each user-submitted
                            trait name was mapped                         to a MedGen CUI identifier
                            and a preferred name.                         The structure may be used
                            in the future to report, when a trait is identified by a source's
                            identifier                         (e.g. MIM number), the preferred name
                            used by that source at the time of submission.
                            For MappingType XRef, MappingRef is the database name and MappingValue
                            is the database's identifier.                         For MappingType
                            Name, MappingRef is Alternate or Preferred, and MappingValue is the
                            submitted name of the trait.                         ClinicalAssertionID
                            is an integer identifier that corresponds 1:1 to the SCV assigned to the
                            submission.                     </xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded" name="TraitMapping">
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="MedGen">
                                            <xs:complexType>
                                                <xs:attribute name="Name" type="xs:string"
                                                  use="required"/>
                                                <xs:attribute name="CUI" type="xs:string"
                                                  use="required"/>
                                            </xs:complexType>
                                        </xs:element>
                                    </xs:sequence>
                                    <xs:attribute name="ClinicalAssertionID"
                                        type="xs:positiveInteger"/>
                                    <xs:attribute name="TraitType" type="xs:string"/>
                                    <xs:attribute name="MappingType">
                                        <xs:simpleType>
                                            <xs:restriction base="xs:string">
                                                <xs:enumeration value="Name"/>
                                                <xs:enumeration value="XRef"/>
                                            </xs:restriction>
                                        </xs:simpleType>
                                    </xs:attribute>
                                    <xs:attribute name="MappingValue" type="xs:string"/>
                                    <xs:attribute name="MappingRef" type="xs:string"/>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element minOccurs="0" name="DeletedSCVList">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded" minOccurs="1" name="SCV"
                                type="typeDeletedSCV"/>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="unbounded" minOccurs="0" name="GeneralCitations">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded" minOccurs="0" name="Citation"
                                type="typeCitation"/>
                            <xs:element maxOccurs="unbounded" minOccurs="0" name="XRef"
                                type="typeXref"/>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="IncludedRecord">
        <xs:annotation>
            <xs:documentation>This element is used for alleles that were not directly part of a
                submission but were             part of a complex submission. They have no direct
                submitted classification, but are being reported for a complete
                representation of all alleles in ClinVar. Compare to
                ClassifiedRecord.</xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:choice>
                    <xs:element name="SimpleAllele" type="typeAllele"/>
                    <xs:element maxOccurs="1" minOccurs="1" name="Haplotype" type="typeHaplotype">
                        <xs:annotation>
                            <xs:documentation>Describes a single sequence change relative to a
                                contiguous region of a chromosome or the mitochondrion.
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:choice>
                <xs:element minOccurs="1" name="Classifications"
                    type="typeAggregateClassificationSet"/>
                <xs:element maxOccurs="1" minOccurs="1" name="SubmittedClassificationList">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded" minOccurs="1" name="SCV"
                                type="typeSCV">
                                <xs:annotation>
                                    <xs:documentation>Maintains the list of SCV accessions and
                                        titles to                               support indexing and
                                        retrieval This structure does not report RCV/SCV
                                        relationships</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="1" name="ClassifiedVariationList">
                    <xs:annotation>
                        <xs:documentation>Maintains the list of classified variants represented in
                            this                      submission,  although not submitted with an
                            Classification                      independently.</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded" minOccurs="1"
                                name="ClassifiedVariation">
                                <xs:complexType>
                                    <xs:attribute name="VariationID" type="xs:positiveInteger"
                                        use="required"/>
                                    <xs:attribute name="Accession" type="xs:string" use="optional"/>
                                    <xs:attribute name="Version" type="xs:integer" use="required"/>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="unbounded" minOccurs="0" name="GeneralCitations">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded" minOccurs="0" name="Citation"
                                type="typeCitation"/>
                            <xs:element maxOccurs="unbounded" minOccurs="0" name="XRef"
                                type="typeXref"/>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:simpleType name="typeGeneVariant">
        <xs:restriction base="xs:string">
            <xs:enumeration value="variant within gene"/>
            <xs:enumeration value="gene overlapped by variant"/>
            <xs:enumeration value="variant near gene, upstream"/>
            <xs:enumeration value="variant near gene, downstream"/>
            <xs:enumeration value="asserted, but not computed"/>
            <xs:enumeration value="within multiple genes by overlap"/>
            <xs:enumeration value="within single gene"/>
            <!-- those are here for back-compatibility and should be removed in the future once clinvar_public.xml is updated -->
            <xs:enumeration value="genes overlapped by variant"/>
            <xs:enumeration value="near gene, downstream"/>
            <xs:enumeration value="near gene, upstream"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="typeSeverity">
        <xs:restriction base="xs:string">
            <xs:enumeration value="mild"/>
            <xs:enumeration value="moderate"/>
            <xs:enumeration value="severe"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="typeStatus">
        <xs:restriction base="xs:string">
            <xs:enumeration value="current"/>
            <xs:enumeration value="completed and retired"/>
            <xs:enumeration value="delete"/>
            <xs:enumeration value="in development"/>
            <xs:enumeration value="reclassified"/>
            <xs:enumeration value="reject"/>
            <xs:enumeration value="secondary"/>
            <xs:enumeration value="suppressed"/>
            <xs:enumeration value="under review"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="typeSubmitterReviewStatusValue">
        <xs:restriction base="xs:string">
            <xs:enumeration value="no classification provided"/>
            <xs:enumeration value="no assertion criteria provided"/>
            <xs:enumeration value="criteria provided, single submitter"/>
            <xs:enumeration value="reviewed by expert panel"/>
            <xs:enumeration value="practice guideline"/>
            <xs:enumeration value="flagged submission"/>
            <!--The following values are added for backward compatibility from clinvar_public.xsd-->
            <xs:enumeration value="criteria provided, multiple submitters, no conflicts"/>
            <xs:enumeration value="criteria provided, conflicting classifications"/>
            <xs:enumeration value="classified by single submitter"/>
            <xs:enumeration value="reviewed by professional society"/>
            <xs:enumeration value="not classified by submitter"/>
            <xs:enumeration value="classified by multiple submitters"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="typeZygosity">
        <xs:restriction base="xs:string">
            <xs:enumeration value="Homozygote"/>
            <xs:enumeration value="SingleHeterozygote"/>
            <xs:enumeration value="CompoundHeterozygote"/>
            <xs:enumeration value="Hemizygote"/>
            <xs:enumeration value="not provided"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="typeAssertionTypeAttr">
        <xs:restriction base="xs:string">
            <xs:enumeration value="variation to disease"/>
            <xs:enumeration value="variation to included disease"/>
            <xs:enumeration value="variation in modifier gene to disease"/>
            <xs:enumeration value="confers sensitivity"/>
            <xs:enumeration value="confers resistance"/>
            <xs:enumeration value="variant to named protein"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="typeAggregateGermlineReviewStatusValue">
        <xs:restriction base="xs:string">
            <xs:enumeration value="no classification provided"/>
            <xs:enumeration value="no assertion criteria provided"/>
            <xs:enumeration value="criteria provided, single submitter"/>
            <xs:enumeration value="criteria provided, multiple submitters, no conflicts"/>
            <xs:enumeration value="criteria provided, conflicting classifications"/>
            <xs:enumeration value="reviewed by expert panel"/>
            <xs:enumeration value="practice guideline"/>
            <xs:enumeration value="no classifications from unflagged records"/>
            <xs:enumeration value="no classification for the single variant"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="typeAggregateSomaticClinicalImpactReviewStatusValue">
        <xs:restriction base="xs:string">
            <xs:enumeration value="no classification provided"/>
            <xs:enumeration value="no assertion criteria provided"/>
            <xs:enumeration value="criteria provided, single submitter"/>
            <xs:enumeration value="criteria provided, multiple submitters"/>
            <xs:enumeration value="reviewed by expert panel"/>
            <xs:enumeration value="practice guideline"/>
            <xs:enumeration value="no classifications from unflagged records"/>
            <xs:enumeration value="no classification for the single variant"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="typeAggregateOncogenicityReviewStatusValue">
        <xs:restriction base="xs:string">
            <xs:enumeration value="no classification provided"/>
            <xs:enumeration value="no assertion criteria provided"/>
            <xs:enumeration value="criteria provided, single submitter"/>
            <xs:enumeration value="criteria provided, multiple submitters, no conflicts"/>
            <xs:enumeration value="criteria provided, conflicting classifications"/>
            <xs:enumeration value="reviewed by expert panel"/>
            <xs:enumeration value="practice guideline"/>
            <xs:enumeration value="no classifications from unflagged records"/>
            <xs:enumeration value="no classification for the single variant"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="typeOrigin">
        <xs:restriction base="xs:string">
            <xs:enumeration value="germline"/>
            <xs:enumeration value="somatic"/>
            <xs:enumeration value="de novo"/>
            <xs:enumeration value="not provided"/>
            <xs:enumeration value="inherited"/>
            <xs:enumeration value="maternal"/>
            <xs:enumeration value="paternal"/>
            <xs:enumeration value="uniparental"/>
            <xs:enumeration value="biparental"/>
            <xs:enumeration value="not-reported"/>
            <xs:enumeration value="tested-inconclusive"/>
            <xs:enumeration value="unknown"/>
            <xs:enumeration value="not applicable"/>
            <xs:enumeration value="experimentally generated"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="typeChromosomeStr">
        <xs:restriction base="xs:string">
            <xs:enumeration value="X"/>
            <xs:enumeration value="Y"/>
            <xs:enumeration value="MT"/>
            <xs:enumeration value="PAR"/>
            <xs:enumeration value="Un"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="typeChromosomeNr">
        <xs:restriction base="xs:int">
            <xs:minInclusive value="1"/>
            <xs:maxInclusive value="22"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="typeChromosome">
        <xs:union memberTypes="typeChromosomeNr typeChromosomeStr"/>
    </xs:simpleType>
    <xs:simpleType name="typeCommentType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="public"/>
            <xs:enumeration value="ConvertedByNCBI"/>
            <xs:enumeration value="MissingFromAssembly"/>
            <xs:enumeration value="GenomicLocationNotEstablished"/>
            <xs:enumeration value="LocationOnGenomeAndProductNotAligned"/>
            <xs:enumeration value="DeletionComment"/>
            <xs:enumeration value="MergeComment"/>
            <xs:enumeration value="AssemblySpecificAlleleDefinition"/>
            <xs:enumeration value="AlignmentGapMakesAppearInconsistent"/>
            <xs:enumeration value="ExplanationOfClassification"/>
            <xs:enumeration value="FlaggedComment"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="typeNucleotideSequence">
        <xs:restriction base="xs:string">
            <xs:enumeration value="genomic, top-level"/>
            <xs:enumeration value="genomic, RefSeqGene"/>
            <xs:enumeration value="genomic"/>
            <xs:enumeration value="coding"/>
            <xs:enumeration value="non-coding"/>
            <xs:enumeration value="protein"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="typeProteinSequence">
        <xs:restriction base="xs:string">
            <xs:enumeration value="protein"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="typePhenotypeSet">
        <xs:restriction base="xs:string">
            <xs:enumeration value="Disease"/>
            <xs:enumeration value="DrugResponse"/>
            <xs:enumeration value="Finding"/>
            <xs:enumeration value="PhenotypeInstruction"/>
            <xs:enumeration value="TraitChoice"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="typeVariationType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="Diplotype"/>
            <xs:enumeration value="CompoundHeterozygote"/>
            <xs:enumeration value="Distinct chromosomes"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="typeEvidencetype">
        <xs:restriction base="xs:string">
            <xs:enumeration value="Genetic"/>
            <xs:enumeration value="Experimental"/>
            <xs:enumeration value="Population"/>
            <xs:enumeration value="Computational"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="typeComment">
        <xs:annotation>
            <xs:documentation>A structure to support reporting unformatted content, with type and
                source specified.</xs:documentation>
        </xs:annotation>
        <xs:simpleContent>
            <xs:extension base="xs:string">
                <xs:attribute name="DataSource" type="xs:string"/>
                <xs:attribute name="Type" type="typeCommentType"/>
            </xs:extension>
        </xs:simpleContent>
    </xs:complexType>
    <xs:complexType name="typeHGVSExpression">
        <xs:annotation>
            <xs:documentation>A structure to represent an HGVS expression for a nucleotide sequence
                variant, along with the predicted protein change             and the predicted
                molecular consequence. Also used to represent only the protein change if that is all
                that has been reported.          </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="NucleotideExpression"
                type="typeNucleotideSequenceExpression"/>
            <xs:element maxOccurs="1" minOccurs="0" name="ProteinExpression"
                type="typeProteinSequenceExpression"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="MolecularConsequence"
                type="typeXref"/>
        </xs:sequence>
        <xs:attribute name="Type" type="typeHGVS" use="required"/>
        <xs:attribute name="Assembly" use="optional"/>
    </xs:complexType>
    <xs:simpleType name="typeMethodlist">
        <xs:restriction base="xs:string">
            <xs:enumeration value="literature only"/>
            <xs:enumeration value="reference population"/>
            <xs:enumeration value="case-control"/>
            <xs:enumeration value="clinical testing"/>
            <xs:enumeration value="in vitro"/>
            <xs:enumeration value="in vivo"/>
            <xs:enumeration value="research"/>
            <xs:enumeration value="curation"/>
            <xs:enumeration value="not provided"/>
            <xs:enumeration value="provider interpretation"/>
            <xs:enumeration value="phenotyping only"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="typeHaplotype">
        <xs:annotation>
            <xs:documentation>This is a record of one or more simple alleles on the same chromosome
                molecule.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="unbounded" minOccurs="1" name="SimpleAllele" type="typeAllele"/>
            <xs:element maxOccurs="1" minOccurs="1" name="Name" type="xs:string">
                <xs:annotation>
                    <xs:documentation>The preferred representation of the
                        haplotype.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="CommonName" type="xs:string"/>
            <xs:element maxOccurs="1" minOccurs="1" name="VariationType"
                type="typeHaplotypeVariationType"/>
            <xs:element minOccurs="0" name="OtherNameList" type="typeNames">
                <xs:annotation>
                    <xs:documentation>Names other than 'preferred' used for the
                        haplotype.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element minOccurs="0" name="HGVSlist">
                <xs:annotation>
                    <xs:documentation>List of  all the HGVS expressions valid for, or used to
                        submit, a variant.</xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element maxOccurs="unbounded" minOccurs="0" name="HGVS"
                            type="typeHGVSExpression"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element minOccurs="0" name="Classifications" type="typeAggregateClassificationSet"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="FunctionalConsequence"
                type="typeFunctionalConsequence"/>
            <xs:element minOccurs="0" name="XRefList">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element maxOccurs="unbounded" minOccurs="0" name="XRef" type="typeXref"
                        />
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Comment" type="typeComment"/>
        </xs:sequence>
        <xs:attribute name="VariationID" type="xs:int" use="required"/>
        <xs:attribute name="NumberOfCopies" type="xs:int" use="optional"/>
        <xs:attribute name="NumberOfChromosomes" type="xs:positiveInteger" use="optional"/>
    </xs:complexType>
    <xs:complexType name="typeHaplotypeSCV">
        <xs:sequence>
            <xs:element maxOccurs="unbounded" minOccurs="1" name="SimpleAllele" type="typeAlleleSCV"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Name" type="xs:string"> </xs:element>
            <xs:element minOccurs="0" name="OtherNameList" type="typeNames">
                <xs:annotation>
                    <xs:documentation>Other names used for the haplotype</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element minOccurs="0" name="Classifications" type="typeAggregateClassificationSet"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="FunctionalConsequence"
                type="typeFunctionalConsequence"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="AttributeSet"
                type="typeAttributeSet"/>
            <xs:element minOccurs="0" name="CitationList">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element maxOccurs="unbounded" name="Citation" type="typeCitation"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element minOccurs="0" name="XRefList">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element maxOccurs="unbounded" minOccurs="0" name="XRef" type="typeXref"
                        />
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Comment" type="typeComment"/>
        </xs:sequence>
        <xs:attribute name="VariationID" type="xs:int" use="optional"/>
        <xs:attribute name="NumberOfCopies" type="xs:int" use="optional"/>
        <xs:attribute name="NumberOfChromosomes" type="xs:positiveInteger" use="optional"/>
    </xs:complexType>
    <xs:complexType name="typeAttribute">
        <xs:annotation>
            <xs:documentation>The attribute is a general element to represent a defined set of data
                qualified by an enumerated set of types. For each attribute element, the value will
                be a             character string and is optional. Source shall be used to store
                identifiers for supplied             data from source other than the submitter (e.g.
                SequenceOntology). The data submitted             where Type="variation" shall be
                validated against sequence_alternation in Sequence             Ontology
                http://www.sequenceontology.org/. This is to be a generic version of
                AttributeType and should be used with extension when it is used to specify Type and
                its             enumerations. </xs:documentation>
        </xs:annotation>
        <xs:simpleContent>
            <xs:extension base="xs:string">
                <xs:attribute name="integerValue" type="xs:int" use="optional"/>
                <xs:attribute name="dateValue" type="xs:date" use="optional"/>
            </xs:extension>
        </xs:simpleContent>
    </xs:complexType>
    <xs:complexType name="typeAlleleDescr">
        <xs:annotation>
            <xs:documentation>This is to be used within co-occurrence set </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="Name" type="xs:string"/>
            <xs:element maxOccurs="1" minOccurs="0" name="RelativeOrientation">
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                        <xs:enumeration value="cis"/>
                        <xs:enumeration value="trans"/>
                        <xs:enumeration value="unknown"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Zygosity" type="typeZygosity"/>
            <xs:element maxOccurs="1" minOccurs="0" name="ClinicalSignificance"
                type="ClinicalSignificanceType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="typeLocation">
        <xs:annotation>
            <xs:documentation>There can be multiple types of location, and the locations may have
                identifiers in other databases </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="4" minOccurs="0" name="CytogeneticLocation" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Cytogenetic location is maintained independent of sequence
                        location, and can be submitted or computed from the sequence
                        location</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="SequenceLocation">
                <xs:annotation>
                    <xs:documentation>Location on a defined sequence, with reference and alternate
                        allele, and start /stop values depending on the specificity with which the
                        variant                   location is known. The number system of offset 1,
                        and right-justified to be                   consistent with HGVS location
                        data</xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:attribute name="forDisplay" type="xs:boolean" use="optional"/>
                    <xs:attribute name="Assembly" type="xs:string" use="required"/>
                    <xs:attribute name="Chr" type="typeChromosome" use="required"/>
                    <xs:attribute name="Accession" type="xs:string" use="optional"/>
                    <xs:attribute name="outerStart" type="xs:nonNegativeInteger" use="optional"/>
                    <xs:attribute name="innerStart" type="xs:nonNegativeInteger" use="optional"/>
                    <xs:attribute name="start" type="xs:nonNegativeInteger" use="optional"/>
                    <xs:attribute name="stop" type="xs:positiveInteger" use="optional"/>
                    <xs:attribute name="innerStop" type="xs:positiveInteger" use="optional"/>
                    <xs:attribute name="outerStop" type="xs:positiveInteger" use="optional"/>
                    <xs:attribute name="display_start" type="xs:nonNegativeInteger" use="optional"/>
                    <xs:attribute name="display_stop" type="xs:positiveInteger" use="optional"/>
                    <xs:attribute name="Strand" type="xs:string" use="optional"/>
                    <xs:attribute name="variantLength" type="xs:nonNegativeInteger" use="optional"/>
                    <xs:attribute name="referenceAllele" type="xs:string" use="optional"/>
                    <xs:attribute name="alternateAllele" type="xs:string" use="optional"/>
                    <xs:attribute name="AssemblyAccessionVersion" type="xs:string" use="optional"/>
                    <xs:attribute name="AssemblyStatus" use="optional">
                        <xs:simpleType>
                            <xs:restriction base="xs:string">
                                <xs:enumeration value="current"/>
                                <xs:enumeration value="previous"/>
                            </xs:restriction>
                        </xs:simpleType>
                    </xs:attribute>
                    <xs:attribute name="positionVCF" type="xs:nonNegativeInteger" use="optional"/>
                    <xs:attribute name="referenceAlleleVCF" type="xs:string" use="optional"/>
                    <xs:attribute name="alternateAlleleVCF" type="xs:string" use="optional"/>
                    <xs:attribute name="forDisplayLength" type="xs:boolean" use="optional"/>
                </xs:complexType>
            </xs:element>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="GeneLocation" type="xs:string">
                <xs:annotation>
                    <xs:documentation>The location of the variant relative to features within the
                        gene</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="XRef" type="typeXref"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="typeAllele">
        <xs:annotation>
            <xs:documentation>This is a record per variant (Measure/@ID,AlleleID)</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="GeneList">
                <xs:annotation>
                    <xs:documentation>0 to many genes (and related data ) related to the allele
                        being                   reported. </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element maxOccurs="unbounded" minOccurs="0" name="Gene">
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element minOccurs="0" name="Location" type="typeLocation"/>
                                    <xs:element maxOccurs="unbounded" minOccurs="0" name="OMIM"
                                        type="xs:positiveInteger"> </xs:element>
                                    <xs:element maxOccurs="1" minOccurs="0"
                                        name="Haploinsufficiency">
                                        <xs:complexType>
                                            <xs:simpleContent>
                                                <xs:extension base="xs:string">
                                                  <xs:attribute name="last_evaluated" type="xs:date"
                                                  use="optional"/>
                                                  <xs:attribute name="ClinGen" type="xs:anyURI"
                                                  use="optional"/>
                                                </xs:extension>
                                            </xs:simpleContent>
                                        </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1" minOccurs="0" name="Triplosensitivity">
                                        <xs:complexType>
                                            <xs:simpleContent>
                                                <xs:extension base="xs:string">
                                                  <xs:attribute name="last_evaluated" type="xs:date"
                                                  use="optional"/>
                                                  <xs:attribute name="ClinGen" type="xs:anyURI"
                                                  use="optional"/>
                                                </xs:extension>
                                            </xs:simpleContent>
                                        </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="unbounded" minOccurs="0" name="Property"
                                        type="xs:string">
                                        <xs:annotation>
                                            <xs:documentation>Used to set key words for retrieval or
                                                display                                     about a
                                                gene, such as genes listed by the ACMG
                                                guidelines.</xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                </xs:sequence>
                                <xs:attribute name="Symbol" use="optional"/>
                                <xs:attribute name="FullName" type="xs:string" use="required"> </xs:attribute>
                                <xs:attribute name="GeneID" type="xs:positiveInteger" use="required"/>
                                <xs:attribute name="HGNC_ID" type="xs:string" use="optional"/>
                                <xs:attribute name="Source" type="xs:string" use="required">
                                    <xs:annotation>
                                        <xs:documentation>calculated or submitted</xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:attribute name="RelationshipType" type="typeGeneVariant"
                                    use="optional"/>
                            </xs:complexType>
                        </xs:element>
                    </xs:sequence>
                    <xs:attribute name="GeneCount" type="xs:int" use="optional"/>
                </xs:complexType>
            </xs:element>
            <xs:element name="Name" type="xs:string"> </xs:element>
            <xs:element minOccurs="0" name="CommonName" type="xs:string"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CanonicalSPDI" type="xs:string"/>
            <xs:element minOccurs="0" name="VariantType" type="xs:string"> </xs:element>
            <xs:element minOccurs="0" name="Location" type="typeLocation"/>
            <xs:element minOccurs="0" name="OtherNameList" type="typeNames"> </xs:element>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="ProteinChange" type="xs:string">
                <xs:annotation>
                    <xs:documentation>These are the single-letter representations of the protein
                        change.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element minOccurs="0" name="HGVSlist">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element maxOccurs="unbounded" minOccurs="0" name="HGVS"
                            type="typeHGVSExpression"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element minOccurs="0" name="Classifications" type="typeAggregateClassificationSet"/>
            <xs:element minOccurs="0" name="XRefList">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element maxOccurs="unbounded" minOccurs="0" name="XRef" type="typeXref"
                        />
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Comment" type="typeComment"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="FunctionalConsequence"
                type="typeFunctionalConsequence"/>
            <xs:element minOccurs="0" name="AlleleFrequencyList">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element maxOccurs="unbounded" name="AlleleFrequency">
                            <xs:complexType>
                                <xs:attribute name="Value" type="xs:double"/>
                                <xs:attribute name="Source" type="xs:string"/>
                                <xs:attribute name="URL" type="xs:anyURI" use="optional"/>
                            </xs:complexType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="GlobalMinorAlleleFrequency">
                <xs:complexType>
                    <xs:attribute name="Value" type="xs:double"/>
                    <xs:attribute name="Source" type="xs:string"/>
                    <xs:attribute name="MinorAllele" type="xs:string" use="optional"/>
                    <xs:attribute name="URL" type="xs:anyURI" use="optional"/>
                </xs:complexType>
            </xs:element>
        </xs:sequence>
        <xs:attribute name="AlleleID" type="xs:int" use="required"/>
        <xs:attribute name="VariationID" type="xs:positiveInteger" use="required"/>
    </xs:complexType>
    <xs:complexType name="typeAlleleSCV">
        <xs:annotation>
            <xs:documentation>This is a record per variant (Measure/@ID,AlleleID) as submitted for
                accessioning in an SCV</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="GeneList">
                <xs:annotation>
                    <xs:documentation>0 to many genes (and related data ) related to the allele
                        being                   reported. </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element maxOccurs="unbounded" minOccurs="0" name="Gene">
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element minOccurs="0" name="Name" type="xs:string"/>
                                    <xs:element maxOccurs="unbounded" minOccurs="0" name="Property"
                                        type="xs:string">
                                        <xs:annotation>
                                            <xs:documentation>Used to set key words for retrieval or
                                                display                                     about a
                                                gene, such as genes listed by the ACMG
                                                guidelines.</xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="unbounded" minOccurs="0" name="XRef"
                                        type="typeXref">
                                        <xs:annotation>
                                            <xs:documentation>Used for gene specific identifiers
                                                such as MIM
                                                number, Gene ID, HGNC ID, etc.</xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                </xs:sequence>
                                <xs:attribute name="Symbol" use="optional"/>
                                <xs:attribute name="RelationshipType" type="typeGeneVariant"
                                    use="optional"/>
                            </xs:complexType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Name">
                <xs:complexType>
                    <xs:annotation>
                        <xs:documentation>Name provided by the submitter.</xs:documentation>
                    </xs:annotation>
                    <xs:simpleContent>
                        <xs:extension base="xs:string">
                            <xs:attribute name="Type" type="xs:string"/>
                        </xs:extension>
                    </xs:simpleContent>
                </xs:complexType>
            </xs:element>
            <xs:element minOccurs="0" name="VariantType" type="xs:string"/>
            <xs:element minOccurs="0" name="Location" type="typeLocation"/>
            <xs:element minOccurs="0" name="OtherNameList" type="typeNames"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="ProteinChange" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Single letter representation of the amino acid change and its
                        location.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element minOccurs="0" name="XRefList">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element maxOccurs="unbounded" minOccurs="0" name="XRef" type="typeXref"
                        />
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element minOccurs="0" name="CitationList">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element maxOccurs="unbounded" name="Citation" type="typeCitation"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Comment" type="typeComment"/>
            <xs:element minOccurs="0" name="MolecularConsequenceList">
                <xs:annotation>
                    <xs:documentation>Currently redundant with the MolecularConsequence element of
                        the HGVS element?</xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element maxOccurs="unbounded" name="MolecularConsequence">
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element maxOccurs="unbounded" minOccurs="0" name="XRef"
                                        type="typeXref"/>
                                    <xs:element maxOccurs="unbounded" minOccurs="0" name="Citation"
                                        type="typeCitation"/>
                                    <xs:element maxOccurs="unbounded" minOccurs="0" name="Comment"
                                        type="typeComment"/>
                                </xs:sequence>
                                <xs:attribute name="RS" type="xs:positiveInteger" use="optional"/>
                                <!-- optional because will calculate consequence from HGVS-->
                                <xs:attribute name="HGVS" type="xs:string" use="optional"/>
                                <xs:attribute name="SOid" type="xs:string" use="optional"/>
                                <xs:attribute name="Function" type="xs:string"> </xs:attribute>
                            </xs:complexType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="FunctionalConsequence"
                type="typeFunctionalConsequence"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="AttributeSet"
                type="typeAttributeSet"/>
        </xs:sequence>
        <xs:attribute name="AlleleID" type="xs:int" use="optional"/>
    </xs:complexType>
    <xs:complexType name="typeGenotype">
        <xs:annotation>
            <xs:documentation>Used to report genotypes, be they simple or complex
                diplotypes.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:choice maxOccurs="unbounded" minOccurs="1">
                <xs:element maxOccurs="1" minOccurs="1" name="SimpleAllele" type="typeAllele">
                    <xs:annotation>
                        <xs:documentation> Describes a single sequence change relative to a
                            contiguous region of a chromosome or the mitochondrion.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="1" name="Haplotype" type="typeHaplotype">
                    <xs:annotation>
                        <xs:documentation>Allow more than 2 haplotypes per genotype to support
                            representation                     of ploidy.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:choice>
            <xs:element maxOccurs="1" minOccurs="1" name="Name" type="xs:string"/>
            <xs:element minOccurs="0" name="CommonName" type="xs:string"/>
            <xs:element maxOccurs="1" minOccurs="1" name="VariationType" type="typeVariationType"/>
            <xs:element minOccurs="0" name="OtherNameList" type="typeNames">
                <xs:annotation>
                    <xs:documentation>Names other than 'preferred' used for the
                        Genotype.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element minOccurs="0" name="HGVSlist">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element maxOccurs="unbounded" minOccurs="0" name="HGVS"
                            type="typeHGVSExpression"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="FunctionalConsequence">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element maxOccurs="unbounded" minOccurs="0" name="XRef" type="typeXref"/>
                        <xs:element maxOccurs="unbounded" minOccurs="0" name="Citation"
                            type="typeCitation"/>
                        <xs:element maxOccurs="unbounded" minOccurs="0" name="Comment"
                            type="typeComment"/>
                    </xs:sequence>
                    <xs:attribute name="Value" type="xs:string" use="required"/>
                </xs:complexType>
            </xs:element>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Classifications"
                type="typeAggregateClassificationSet"/>
            <xs:element minOccurs="0" name="XRefList">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element maxOccurs="unbounded" minOccurs="0" name="XRef" type="typeXref"
                        />
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element minOccurs="0" name="CitationList">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element maxOccurs="unbounded" name="Citation" type="typeCitation"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Comment" type="typeComment"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="AttributeSet"
                type="typeAttributeSet"/>
        </xs:sequence>
        <xs:attribute name="VariationID" type="xs:int" use="required"/>
    </xs:complexType>
    <xs:complexType name="typeXref">
        <xs:annotation>
            <xs:documentation>This structure is used to represent how an object described in the
                submission relates to objects in other databases.</xs:documentation>
        </xs:annotation>
        <xs:attribute name="DB" type="xs:string" use="required">
            <xs:annotation>
                <xs:documentation>The name of the database. When there is an overlap with sequence
                    databases, that name is used.</xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="ID" type="xs:string" use="required">
            <xs:annotation>
                <xs:documentation>The identifier used by the database. Being exported as a string
                    even                though internally the database has rules for defining which
                    datases use integer                identifers.</xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="Type" type="xs:string" use="optional">
            <xs:annotation>
                <xs:documentation>Used to differentiate between different types of identifiers that
                    a                database may provide.</xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="URL" type="xs:anyURI" use="optional"/>
        <xs:attribute default="current" name="Status" type="typeStatus" use="optional"/>
    </xs:complexType>
    <xs:complexType name="SubmitterType">
        <xs:annotation>
            <xs:documentation>A structure to support reporting the name of a submitter, its
                organization id, and its abbreviation and type</xs:documentation>
        </xs:annotation>
        <xs:attributeGroup ref="SubmitterIdentifiers"/>
        <xs:attribute name="Type" use="required">
            <xs:simpleType>
                <xs:restriction base="xs:string">
                    <xs:enumeration value="primary"/>
                    <xs:enumeration value="secondary"/>
                    <xs:enumeration value="behalf"/>
                </xs:restriction>
            </xs:simpleType>
        </xs:attribute>
    </xs:complexType>
    <xs:complexType name="typeNames">
        <xs:sequence>
            <xs:element maxOccurs="unbounded" name="Name" nillable="false">
                <xs:complexType>
                    <xs:simpleContent>
                        <xs:extension base="xs:string">
                            <xs:attribute name="Type" type="xs:string" use="optional"/>
                        </xs:extension>
                    </xs:simpleContent>
                </xs:complexType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="typeHGVS">
        <xs:restriction base="xs:string">
            <xs:enumeration value="coding"/>
            <xs:enumeration value="genomic"/>
            <xs:enumeration value="genomic, top-level"/>
            <xs:enumeration value="non-coding"/>
            <xs:enumeration value="protein"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="typeEvidenceObservation">
        <xs:sequence>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Method" type="typeMethodRefs">
                <xs:annotation>
                    <xs:documentation>Method of data capture, not method of
                        evaluation.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="ModeOfInheritance"
                type="xs:string"/>
            <xs:element minOccurs="0" name="CitationList">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element maxOccurs="unbounded" name="Citation" type="typeCitation"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Description" type="typeComment"/>
            <xs:element minOccurs="0" name="ObservedPhenotypes" type="PhenotypeListDetailsType"/>
            <xs:element minOccurs="0" name="Indications" type="IndicationListType"/>
        </xs:sequence>
        <xs:attributeGroup ref="SubmitterIdentifiers"/>
        <xs:attribute name="Families"> </xs:attribute>
        <xs:attribute name="Individuals"> </xs:attribute>
        <xs:attribute name="Segregation"> </xs:attribute>
        <xs:attribute name="OtherGene"> </xs:attribute>
        <xs:attribute name="SameGene"> </xs:attribute>
        <xs:attribute name="Type" type="typeEvidencetype" use="required"/>
        <xs:attribute name="AlleleFrequency" type="xs:string"/>
        <xs:attribute name="AlleleOrigin" type="typeOrigin"/>
        <xs:attribute name="AlleleOriginTimesObserved" type="xs:int"/>
        <xs:attribute name="Ethnicity" type="xs:string"/>
        <xs:attribute name="GeographicOrigin" type="xs:string"/>
    </xs:complexType>
    <xs:complexType name="typeCitation">
        <xs:sequence>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="ID">
                <xs:complexType>
                    <xs:simpleContent>
                        <xs:extension base="xs:string">
                            <xs:attribute name="Source" type="xs:string" use="required">
                                <xs:annotation>
                                    <xs:documentation>If there is an identifier, what database
                                        provides
                                        it.</xs:documentation>
                                </xs:annotation>
                            </xs:attribute>
                        </xs:extension>
                    </xs:simpleContent>
                </xs:complexType>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="URL" type="xs:anyURI"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CitationText" type="xs:string"/>
        </xs:sequence>
        <xs:attribute name="Type" type="xs:string" use="optional">
            <xs:annotation>
                <xs:documentation>This maintained distinct from publication types in PubMed and
                    established by GTR curators. The default is 'general'.</xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="Abbrev" type="xs:string" use="optional">
            <xs:annotation>
                <xs:documentation>Corresponds to the abbreviation reported by
                    GTR.</xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>
    <xs:complexType name="typeNucleotideSequenceExpression">
        <xs:sequence>
            <xs:element name="Expression" type="xs:string"/>
        </xs:sequence>
        <xs:attribute name="sequenceType" type="typeNucleotideSequence"/>
        <xs:attribute name="sequenceAccessionVersion" use="optional"/>
        <xs:attribute name="sequenceAccession" use="optional"/>
        <xs:attribute name="sequenceVersion" use="optional"/>
        <xs:attribute name="change" use="optional"/>
        <xs:attribute name="Assembly" use="optional"/>
        <xs:attribute name="Submitted" use="optional"/>
        <xs:attribute name="MANESelect" type="xs:boolean" use="optional"/>
        <xs:attribute name="MANEPlusClinical" type="xs:boolean" use="optional"/>
    </xs:complexType>
    <xs:complexType name="typeProteinSequenceExpression">
        <xs:sequence>
            <xs:element name="Expression" type="xs:string"/>
        </xs:sequence>
        <xs:attribute name="sequenceAccessionVersion" use="optional"/>
        <xs:attribute name="sequenceAccession" use="optional"/>
        <xs:attribute name="sequenceVersion" use="optional"/>
        <xs:attribute name="change" use="optional"/>
    </xs:complexType>
    <xs:complexType name="PhenotypeListDetailsType">
        <xs:sequence>
            <xs:element maxOccurs="unbounded" name="Phenotype">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element minOccurs="0" name="XRefList">
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element maxOccurs="unbounded" minOccurs="0" name="XRef"
                                        type="typeXref"/>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                    </xs:sequence>
                    <xs:attribute name="Name" type="xs:string" use="required"/>
                    <xs:attribute name="target_id" type="xs:int" use="required"/>
                    <xs:attribute name="AffectedStatus" type="xs:string" use="optional"/>
                </xs:complexType>
            </xs:element>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="PhenotypeDetails">
                <xs:annotation>
                    <xs:documentation>The set of descriptors and values for person-specific
                        phenotype                   information.</xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element minOccurs="0" name="XRefList">
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element maxOccurs="unbounded" minOccurs="0" name="XRef"
                                        type="typeXref"/>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                    </xs:sequence>
                    <xs:attributeGroup ref="SubmitterIdentifiers"/>
                    <xs:attribute name="Type" type="xs:string" use="required">
                        <xs:annotation>
                            <xs:documentation>Type is used to separate biochemical tests from
                                clinical                         features with
                                attributes.</xs:documentation>
                        </xs:annotation>
                    </xs:attribute>
                    <xs:attribute name="PersonID" type="xs:string" use="required"/>
                    <xs:attribute name="DateEvaluated" type="xs:date" use="optional"/>
                    <xs:attribute name="PhenotypeName" type="xs:string" use="optional"/>
                    <xs:attribute name="AffectedStatus" type="xs:string" use="optional"/>
                    <xs:attribute name="LOINC" type="xs:string" use="optional"/>
                    <xs:attribute name="ObservedValue" type="xs:string" use="optional"/>
                    <xs:attribute name="Interpretation" type="xs:string" use="optional"/>
                    <xs:attribute name="SourceLaboratory" type="xs:string" use="optional"/>
                </xs:complexType>
            </xs:element>
        </xs:sequence>
        <xs:attribute name="PhenotypeSetType" type="typePhenotypeSet"/>
    </xs:complexType>
    <xs:complexType name="IndicationListType">
        <xs:sequence>
            <xs:element maxOccurs="unbounded" name="Indication">
                <xs:annotation>
                    <xs:documentation>The indication may be a set of phenotypic
                        descriptions.</xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element minOccurs="0" name="XRefList">
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element maxOccurs="unbounded" minOccurs="0" name="XRef"
                                        type="typeXref"/>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                    </xs:sequence>
                    <xs:attribute name="Name" type="xs:string" use="required"/>
                    <xs:attribute name="target_id" type="xs:int" use="required"/>
                </xs:complexType>
            </xs:element>
        </xs:sequence>
        <xs:attribute name="PhenotypeSetType" type="typePhenotypeSet"/>
    </xs:complexType>
    <xs:complexType name="PharmaType">
        <xs:sequence>
            <xs:element maxOccurs="unbounded" name="DrugResponse">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element minOccurs="0" name="PhenotypeList"
                            type="PhenotypeListDetailsType"/>
                        <xs:element minOccurs="0" name="XRefList">
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element maxOccurs="unbounded" minOccurs="0" name="XRef"
                                        type="typeXref"/>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                    </xs:sequence>
                    <xs:attribute name="Name" type="xs:string" use="required"/>
                    <xs:attribute name="target_id" type="xs:int" use="optional"/>
                </xs:complexType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="typeMethodRefs">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="1" name="MethodName" type="xs:string"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="XRef" type="typeXref"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="typeSample">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="SampleDescription">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element maxOccurs="1" minOccurs="0" name="Description"
                            type="typeComment"/>
                        <xs:element maxOccurs="1" minOccurs="0" name="Citation" type="typeCitation"
                        />
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element maxOccurs="1" name="Origin">
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                        <xs:enumeration value="germline"/>
                        <xs:enumeration value="somatic"/>
                        <xs:enumeration value="de novo"/>
                        <xs:enumeration value="unknown"/>
                        <xs:enumeration value="not provided"/>
                        <xs:enumeration value="inherited"/>
                        <xs:enumeration value="maternal"/>
                        <xs:enumeration value="paternal"/>
                        <xs:enumeration value="uniparental"/>
                        <xs:enumeration value="biparental"/>
                        <xs:enumeration value="not-reported"/>
                        <xs:enumeration value="tested-inconclusive"/>
                        <xs:enumeration value="not applicable"/>
                        <xs:enumeration value="experimentally generated"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Ethnicity" type="xs:string"/>
            <xs:element maxOccurs="1" minOccurs="0" name="GeographicOrigin" type="xs:string"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Tissue" type="xs:string"/>
            <xs:element maxOccurs="1" minOccurs="0" name="SomaticVariantInNormalTissue">
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                        <xs:enumeration value="present"/>
                        <xs:enumeration value="absent"/>
                        <xs:enumeration value="not tested"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="SomaticVariantAlleleFraction"
                type="xs:string"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CellLine" type="xs:string"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Species">
                <xs:complexType>
                    <xs:simpleContent>
                        <xs:extension base="xs:string">
                            <xs:attribute name="TaxonomyId" type="xs:int" use="optional"/>
                        </xs:extension>
                    </xs:simpleContent>
                </xs:complexType>
            </xs:element>
            <xs:element maxOccurs="2" minOccurs="0" name="Age">
                <xs:complexType>
                    <xs:simpleContent>
                        <xs:extension base="xs:int">
                            <xs:attribute name="age_unit" use="required">
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                        <xs:enumeration value="days"/>
                                        <xs:enumeration value="weeks"/>
                                        <xs:enumeration value="months"/>
                                        <xs:enumeration value="years"/>
                                        <xs:enumeration value="weeks gestation"/>
                                        <xs:enumeration value="months gestation"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:attribute>
                            <xs:attribute name="Type" use="required">
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                        <xs:enumeration value="minimum"/>
                                        <xs:enumeration value="maximum"/>
                                        <xs:enumeration value="single"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:attribute>
                        </xs:extension>
                    </xs:simpleContent>
                </xs:complexType>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Strain" type="xs:string"/>
            <xs:element maxOccurs="1" minOccurs="1" name="AffectedStatus">
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                        <xs:enumeration value="yes"/>
                        <xs:enumeration value="no"/>
                        <xs:enumeration value="not provided"/>
                        <xs:enumeration value="unknown"/>
                        <xs:enumeration value="not applicable"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="NumberTested" type="xs:int">
                <xs:annotation>
                    <xs:documentation>Denominator, total individuals included in this observation
                        set.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="NumberMales" type="xs:int">
                <xs:annotation>
                    <xs:documentation>Denominator, total males included in this observation
                        set.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="NumberFemales" type="xs:int">
                <xs:annotation>
                    <xs:documentation>Denominator, total females included in this observation
                        set.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="NumberChrTested" type="xs:int">
                <xs:annotation>
                    <xs:documentation>Denominator, total number chromosomes tested. Number affected
                        and                   unaffected are captured in the element
                        NumberObserved.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Sex">
                <xs:annotation>
                    <xs:documentation>Sex should be used ONLY if explicit values are not
                        available for                   number of males or females, and there is a
                        need to indicate that the genders in                   the sample are known.
                    </xs:documentation>
                </xs:annotation>
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                        <xs:enumeration value="male"/>
                        <xs:enumeration value="female"/>
                        <xs:enumeration value="mixed"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="FamilyData" type="FamilyInfo"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Proband" type="xs:string"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Indication" type="IndicationType"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Citation" type="typeCitation"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="XRef" type="typeXref"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Comment" type="typeComment"/>
            <xs:element maxOccurs="1" minOccurs="0" name="SourceType">
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                        <xs:enumeration value="submitter-generated"/>
                        <xs:enumeration value="data mining"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="FamilyInfo">
        <xs:annotation>
            <xs:documentation>Structure to describe attributes of any family data in an observation.
                If             the details of the number of families and the de-identified pedigree
                id are not             available, use FamilyHistory to describe what type of family
                data is available. Can also             be used to report 'Yes' or 'No' if there are
                no more details. </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="FamilyHistory" type="xs:string"/>
        </xs:sequence>
        <xs:attribute name="NumFamilies" type="xs:int" use="optional"/>
        <xs:attribute name="NumFamiliesWithVariant" type="xs:int" use="optional"/>
        <xs:attribute name="NumFamiliesWithSegregationObserved" type="xs:int" use="optional"/>
        <xs:attribute name="PedigreeID" type="xs:string" use="optional"/>
        <xs:attribute name="SegregationObserved" type="xs:string" use="optional"/>
    </xs:complexType>
    <xs:complexType name="typeSoftwareSet">
        <xs:attribute name="name" type="xs:string" use="required"/>
        <xs:attribute name="version" type="xs:string" use="optional"/>
        <xs:attribute name="purpose" type="xs:string" use="optional"/>
    </xs:complexType>
    <xs:complexType name="typeSingleGermlineClassification">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="1" name="ReviewStatus"
                type="typeAggregateGermlineReviewStatusValue">
                <xs:annotation>
                    <xs:documentation>The aggregate review status based on all germline submissions
                        for this record.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Description" type="xs:string">
                <xs:annotation>
                    <xs:documentation>We are not providing an enumeration for the values we report
                        for germline classification within the xsd. Details are in https://github.com/ncbi/clinvar/ClassificationOnClinVar.md.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Explanation" type="typeComment">
                <xs:annotation>
                    <xs:documentation>Explanation is used only when the description is 'conflicting
                        data                   from submitters' The element summarizes the
                        conflict.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="XRef" type="typeXref"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Citation" type="typeCitation"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Comment" type="typeComment"/>
        </xs:sequence>
        <!--missing change required to optional-->
    </xs:complexType>
    <xs:complexType name="typeSingleSomaticClinicalImpact">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="1" name="ReviewStatus"
                type="typeAggregateSomaticClinicalImpactReviewStatusValue">
                <xs:annotation>
                    <xs:documentation>The aggregate review status based on all somatic clinical
                        impact submissions for this record.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Description" type="xs:string">
                <xs:annotation>
                    <xs:documentation>We are not providing an enumeration for the values we report for
                        somatic clinical impact classification within the xsd. Details are in https://github.com/ncbi/clinvar/ClassificationOnClinVar.md.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="XRef" type="typeXref"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Citation" type="typeCitation"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Comment" type="typeComment"/>
        </xs:sequence>
        <!--missing change required to optional-->
    </xs:complexType>
    <xs:complexType name="typeSingleOncogenicityClassification">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="1" name="ReviewStatus"
                type="typeAggregateOncogenicityReviewStatusValue">
                <xs:annotation>
                    <xs:documentation>The aggregate review status based on all oncogenic submissions
                        for this record.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Description" type="xs:string">
                <xs:annotation>
                    <xs:documentation>We are not providing an enumeration for the values we report
                        for oncogenicity classification within the xsd. Details are in https://github.com/ncbi/clinvar/ClassificationOnClinVar.md.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Explanation" type="typeComment">
                <xs:annotation>
                    <xs:documentation>Explanation is used only when the description is 'conflicting
                        data                   from submitters' The element summarizes the
                        conflict.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="XRef" type="typeXref"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Citation" type="typeCitation"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Comment" type="typeComment"/>
        </xs:sequence>
        <!--missing change required to optional-->
    </xs:complexType>
    <xs:complexType name="typeAggregateClassificationSet">
        <xs:annotation>
            <xs:documentation>Used to bundle different types of Classifications (germline, oncogenic, somatic clinical impact)
                ; Supports summary as well as submission
                details</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="GermlineClassification"
                type="typeAggregatedGermlineClassification"/>
            <xs:element minOccurs="0" name="SomaticClinicalImpact"
                type="typeAggregatedSomaticClinicalImpact"/>
            <xs:element maxOccurs="1" minOccurs="0" name="OncogenicityClassification"
                type="typeAggregatedOncogenicityClassification"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="typeAggregatedGermlineClassification">
        <xs:complexContent>
            <xs:extension base="typeSingleGermlineClassification">
                <xs:sequence>
                    <xs:element maxOccurs="unbounded" minOccurs="0" name="DescriptionHistory"
                        type="typeDescriptionHistory"/>
                    <xs:element maxOccurs="1" minOccurs="0" name="ConditionList">
                        <xs:complexType>
                            <xs:sequence>
                                <xs:element maxOccurs="unbounded" minOccurs="1" name="TraitSet"
                                    type="ClinAsserTraitSetType"/>
                            </xs:sequence>
                        </xs:complexType>
                    </xs:element>
                </xs:sequence>
                <xs:attribute name="DateLastEvaluated" type="xs:date" use="optional"/>
                <xs:attribute name="DateCreated" type="xs:date"/>
                <xs:attribute name="MostRecentSubmission" type="xs:date"/>
                <xs:attribute name="NumberOfSubmitters" type="xs:nonNegativeInteger" use="required"/>
                <xs:attribute name="NumberOfSubmissions" type="xs:nonNegativeInteger" use="required"
                />
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="typeAggregatedSomaticClinicalImpact">
        <xs:complexContent>
            <xs:extension base="typeSingleSomaticClinicalImpact">
                <xs:sequence>
                    <xs:element maxOccurs="unbounded" minOccurs="0" name="DescriptionHistory"
                        type="typeDescriptionHistory"/>
                    <xs:element maxOccurs="1" minOccurs="0" name="ConditionList">
                        <xs:complexType>
                            <xs:sequence>
                                <xs:element maxOccurs="unbounded" minOccurs="1" name="TraitSet"
                                    type="ClinAsserTraitSetType"/>
                            </xs:sequence>
                        </xs:complexType>
                    </xs:element>
                </xs:sequence>
                <xs:attribute name="DateLastEvaluated" type="xs:date" use="optional"/>
                <xs:attribute name="DateCreated" type="xs:date"/>
                <xs:attribute name="MostRecentSubmission" type="xs:date"/>
                <xs:attribute name="NumberOfSubmitters" type="xs:nonNegativeInteger" use="required"/>
                <xs:attribute name="NumberOfSubmissions" type="xs:nonNegativeInteger" use="required"
                />
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="typeAggregatedOncogenicityClassification">
        <xs:complexContent>
            <xs:extension base="typeSingleOncogenicityClassification">
                <xs:sequence>
                    <xs:element maxOccurs="unbounded" minOccurs="0" name="DescriptionHistory"
                        type="typeDescriptionHistory"/>
                    <xs:element maxOccurs="1" minOccurs="0" name="ConditionList">
                        <xs:complexType>
                            <xs:sequence>
                                <xs:element maxOccurs="unbounded" minOccurs="1" name="TraitSet"
                                    type="ClinAsserTraitSetType"/>
                            </xs:sequence>
                        </xs:complexType>
                    </xs:element>
                </xs:sequence>
                <xs:attribute name="DateLastEvaluated" type="xs:date" use="optional"/>
                <xs:attribute name="DateCreated" type="xs:date"/>
                <xs:attribute name="MostRecentSubmission" type="xs:date"/>
                <xs:attribute name="NumberOfSubmitters" type="xs:nonNegativeInteger" use="required"/>
                <xs:attribute name="NumberOfSubmissions" type="xs:nonNegativeInteger" use="required"
                />
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="typeDescriptionHistory">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="1" name="Description" type="xs:string"/>
        </xs:sequence>
        <xs:attribute name="Dated" type="xs:date" use="optional"/>
    </xs:complexType>
    <xs:complexType name="typeRCV">
        <xs:sequence>
            <xs:element name="ClassifiedConditionList">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element maxOccurs="unbounded" minOccurs="0" name="ClassifiedCondition"
                            type="typeRCVInterpretedCondition"/>
                    </xs:sequence>
                    <xs:attribute name="TraitSetID" type="xs:positiveInteger" use="required"/>
                </xs:complexType>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="1" name="RCVClassifications">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element maxOccurs="1" minOccurs="0" name="GermlineClassification">
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element maxOccurs="1" name="ReviewStatus"
                                        type="typeAggregateGermlineReviewStatusValue">
                                        <xs:annotation>
                                            <xs:documentation>The aggregate review status based on
                                                all germline submissions for this
                                                record.</xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="Description">
                                        <xs:complexType>
                                            <xs:simpleContent>
                                                <xs:extension base="xs:string">
                                                  <xs:attribute name="DateLastEvaluated"
                                                  type="xs:date"/>
                                                  <xs:attribute name="SubmissionCount"
                                                  type="xs:nonNegativeInteger"/>
                                                </xs:extension>
                                            </xs:simpleContent>
                                        </xs:complexType>
                                    </xs:element>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                        <xs:element maxOccurs="1" minOccurs="0" name="SomaticClinicalImpact">
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element maxOccurs="1" name="ReviewStatus"
                                        type="typeAggregateSomaticClinicalImpactReviewStatusValue">
                                        <xs:annotation>
                                            <xs:documentation>The aggregate review status based on
                                                all somatic clinical impact submissions for this
                                                record.</xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="unbounded" name="Description">
                                        <xs:complexType>
                                            <xs:simpleContent>
                                                <xs:extension base="xs:string">
                                                  <xs:attribute name="ClinicalImpactAssertionType"
                                                  type="xs:string" use="optional"/>
                                                  <xs:attribute
                                                  name="ClinicalImpactClinicalSignificance"
                                                  type="xs:string" use="optional"/>
                                                  <xs:attribute name="DateLastEvaluated"
                                                  type="xs:date"/>
                                                  <xs:attribute name="SubmissionCount"
                                                  type="xs:nonNegativeInteger"/>
                                                </xs:extension>
                                            </xs:simpleContent>
                                        </xs:complexType>
                                    </xs:element>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                        <xs:element maxOccurs="1" minOccurs="0" name="OncogenicityClassification">
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element maxOccurs="1" name="ReviewStatus"
                                        type="typeAggregateOncogenicityReviewStatusValue">
                                        <xs:annotation>
                                            <xs:documentation>The aggregate review status based on
                                                all oncogenic submissions for this
                                                record.</xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="Description">
                                        <xs:complexType>
                                            <xs:simpleContent>
                                                <xs:extension base="xs:string">
                                                  <xs:attribute name="DateLastEvaluated"
                                                  type="xs:date"/>
                                                  <xs:attribute name="SubmissionCount"
                                                  type="xs:nonNegativeInteger"/>
                                                </xs:extension>
                                            </xs:simpleContent>
                                        </xs:complexType>
                                    </xs:element>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element minOccurs="0" name="ReplacedList">
                <xs:annotation>
                    <xs:documentation> The list of RCV accessions this RCV record has replaced.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element maxOccurs="unbounded" minOccurs="1" name="Replaced"
                            type="typeRecordHistory"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
        </xs:sequence>
        <xs:attribute name="Title" type="xs:string" use="optional"/>
        <xs:attribute name="Accession" type="xs:string" use="required"/>
        <xs:attribute name="Version" type="xs:integer" use="required"/>
    </xs:complexType>
    <xs:complexType name="typeSCV">
        <xs:attribute name="Title" type="xs:string" use="optional"/>
        <xs:attribute name="Accession" type="xs:string" use="required"/>
        <xs:attribute name="Version" type="xs:integer" use="required"/>
    </xs:complexType>
    <xs:complexType name="typeFunctionalConsequence">
        <xs:sequence>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="XRef" type="typeXref"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Citation" type="typeCitation"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Comment" type="typeComment"/>
        </xs:sequence>
        <xs:attribute name="Value" type="xs:string" use="required"/>
    </xs:complexType>
    <xs:complexType name="VariationArchiveType">
        <xs:annotation>
            <xs:documentation> This element groups the set of data specific to a VariationArchive
                record, namely the summary data of what has been submitted about a VariationID AND
                for Classified records only, the content each submission (SCV) provided.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element default="current" maxOccurs="1" minOccurs="1" name="RecordStatus">
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                        <xs:enumeration value="current"/>
                        <xs:enumeration value="previous"/>
                        <xs:enumeration value="replaced"/>
                        <xs:enumeration value="deleted"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="ReplacedBy" type="typeRecordHistory"/>
            <xs:element minOccurs="0" name="ReplacedList">
                <xs:annotation>
                    <xs:documentation> The list of VCV accessions this record has replaced.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element maxOccurs="unbounded" minOccurs="1" name="Replaced"
                            type="typeRecordHistory"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Comment" type="typeComment"/>
            <xs:element maxOccurs="1" minOccurs="1" name="Species">
                <xs:complexType>
                    <xs:simpleContent>
                        <xs:extension base="xs:string">
                            <xs:attribute name="TaxonomyId" type="xs:int" use="optional"/>
                        </xs:extension>
                    </xs:simpleContent>
                </xs:complexType>
            </xs:element>
            <xs:choice>
                <xs:element maxOccurs="1" minOccurs="1" ref="ClassifiedRecord">
                    <xs:annotation>
                        <xs:documentation> This element describes the classification of a single
                            allele, haplotype, or genotype based on all submissions to ClinVar. This
                            differs from the element IncludedRecord, which describes simple alleles
                            or haplotypes, referenced in ClassifiedRecord, but for which no explicit
                            classification was submitted. Once that variation is described, details
                            are added about the phenotypes being classified, the classification, the
                            submitters providing the classifications, and all supported
                            evidence.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="1" ref="IncludedRecord">
                    <xs:annotation>
                        <xs:documentation> This element describes a single allele or haplotype
                            included in submissions to ClinVar, but for which no explicit
                            classification was submitted. It also references the submissions and the
                            Classified records that include them. </xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:choice>
        </xs:sequence>
        <xs:attribute name="VariationID" type="xs:positiveInteger" use="required"/>
        <xs:attribute name="VariationName" type="xs:string" use="required">
            <xs:annotation>
                <xs:documentation>This is ClinVar's name for the variant. ClinVar uses this term in
                    its web displays. </xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="VariationType" type="xs:string" use="required"/>
        <xs:attribute name="DateCreated" type="xs:date" use="required">
            <xs:annotation>
                <xs:documentation>DateCreated is the date when the record first became public in
                    ClinVar.             </xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="DateLastUpdated" type="xs:date" use="optional">
            <xs:annotation>
                <xs:documentation>The date the record was last updated in the public database. The
                    update may be a change to one of the submitted records (SCVs) or annotation
                    added to the aggregate record by NCBI staff.                This date is
                    independent of a version change; annotated added by NCBI may change without
                    representing a change in the version.             </xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="MostRecentSubmission" type="xs:date" use="optional">
            <xs:annotation>
                <xs:documentation>This date is of the most recent submitted record (SCV) for the
                    VCV; it may reflect a new submitted record or an update to a submitted record.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="Accession" type="xs:string" use="required">
            <xs:annotation>
                <xs:documentation> Accession assigned to the variant, or set of variants, that was
                    Classified. </xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="Version" type="xs:int" use="required"/>
        <xs:attribute name="NumberOfSubmitters" type="xs:nonNegativeInteger" use="required"/>
        <xs:attribute name="NumberOfSubmissions" type="xs:nonNegativeInteger" use="required"/>
        <xs:attribute name="RecordType" use="required">
            <xs:simpleType>
                <xs:restriction base="xs:string">
                    <xs:enumeration value="included"/>
                    <xs:enumeration value="classified"/>
                </xs:restriction>
            </xs:simpleType>
        </xs:attribute>
    </xs:complexType>
    <xs:complexType name="ReleaseType">
        <xs:sequence>
            <xs:element maxOccurs="unbounded" minOccurs="1" name="VariationArchive"
                type="VariationArchiveType"/>
        </xs:sequence>
        <xs:attribute name="ReleaseDate" type="xs:date" use="required"/>
    </xs:complexType>
    <xs:element name="VariationArchive" type="VariationArchiveType">
        <xs:annotation>
            <xs:documentation>Retained as a separate element for internal use by
                ClinVar.</xs:documentation>
        </xs:annotation>
    </xs:element>
    <xs:complexType name="Co-occurrenceType">
        <xs:sequence>
            <xs:annotation>
                <xs:documentation>This refers to the zygosity of the variant being
                    asserted.</xs:documentation>
            </xs:annotation>
            <xs:element maxOccurs="1" minOccurs="0" name="Zygosity" type="typeZygosity"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="AlleleDescSet"
                type="typeAlleleDescr"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Count" type="xs:int"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="typeDeletedSCV">
        <xs:annotation>
            <xs:documentation>A structure to support reporting of an accession, its version, the
                date it was deleted             and a free-text summary of why it was
                deleted.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="1" name="Accession">
                <xs:complexType>
                    <xs:simpleContent>
                        <xs:extension base="xs:string">
                            <xs:attribute name="Version" type="xs:nonNegativeInteger" use="required"/>
                            <xs:attribute name="DateDeleted" type="xs:date" use="required"/>
                        </xs:extension>
                    </xs:simpleContent>
                </xs:complexType>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Description" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="MeasureTraitType">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="1" name="ClinVarSubmissionID">
                <xs:complexType>
                    <xs:attribute name="localKey" type="xs:string" use="required">
                        <xs:annotation>
                            <xs:documentation>The identifier provided by the submitter to facilitate
                                identification of                         records corresponding to
                                their submissions. If not provided by a submitter,
                                NCBI generates one. If provided by submitter, that is represented in
                                localKeyIsSubmitted. </xs:documentation>
                        </xs:annotation>
                    </xs:attribute>
                    <xs:attribute name="title" type="xs:string" use="optional"/>
                    <xs:attribute name="localKeyIsSubmitted" use="optional"/>
                    <xs:attribute name="submittedAssembly" type="xs:string" use="optional"/>
                </xs:complexType>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="1" name="ClinVarAccession">
                <xs:annotation>
                    <xs:documentation>Only SCV accessions will be stored here.</xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:attribute name="Accession" type="xs:string" use="required"/>
                    <xs:attribute name="Version" type="xs:integer" use="required"/>
                    <xs:attribute fixed="SCV" name="Type" type="xs:string">
                        <xs:annotation>
                            <xs:documentation>                         The only valid value here for
                                Type is 'SCV'. This attribute is maintained for consistency with
                                ClinVarFullRelease.                      </xs:documentation>
                        </xs:annotation>
                    </xs:attribute>
                    <xs:attributeGroup ref="SubmitterIdentifiers"/>
                    <xs:attribute name="DateUpdated" type="xs:date">
                        <xs:annotation>
                            <xs:documentation>The date that the latest update to the submitted
                                record (SCV) became public in ClinVar.
                            </xs:documentation>
                        </xs:annotation>
                    </xs:attribute>
                    <xs:attribute name="DateCreated" type="xs:date" use="optional">
                        <xs:annotation>
                            <xs:documentation>DateCreated is the date when the record first became
                                public in ClinVar.                      </xs:documentation>
                        </xs:annotation>
                    </xs:attribute>
                </xs:complexType>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="AdditionalSubmitters">
                <xs:annotation>
                    <xs:documentation>Optional element used only if there are multiple submitters.
                        When                   there are multiple, each is listed in this element.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element maxOccurs="unbounded" minOccurs="1" name="SubmitterDescription"
                            type="SubmitterType"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element default="current" maxOccurs="1" minOccurs="1" name="RecordStatus">
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                        <xs:enumeration value="current"/>
                        <xs:enumeration value="replaced"/>
                        <xs:enumeration value="removed"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:choice>
                <xs:element maxOccurs="unbounded" minOccurs="0" name="Replaces" type="xs:string"/>
                <xs:element minOccurs="0" name="ReplacedList">
                    <xs:annotation>
                        <xs:documentation>                   The list of SCV accessions this SCV
                            record has replaced.                </xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded" minOccurs="1" name="Replaced"
                                type="typeClinicalAssertionRecordHistory"/>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:choice>
            <xs:element minOccurs="1" name="Classification" type="ClassificationTypeSCV"/>
            <xs:element name="Assertion" type="typeAssertionTypeAttr"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="AttributeSet">
                <xs:annotation>
                    <xs:documentation>AttributeSet is a package to represent a unit of information,
                        the                   source(s) of that unit, identifiers representing that
                        unit, and comments.                </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="Attribute">
                            <xs:complexType>
                                <xs:simpleContent>
                                    <xs:extension base="typeAttribute">
                                        <xs:attribute name="Type" use="required">
                                            <xs:simpleType>
                                                <xs:restriction base="xs:string">
                                                  <xs:enumeration value="ModeOfInheritance"/>
                                                  <xs:enumeration value="Penetrance"/>
                                                  <xs:enumeration value="AgeOfOnset"/>
                                                  <xs:enumeration value="Severity"/>
                                                  <xs:enumeration value="ClassificationHistory"/>
                                                  <xs:enumeration value="SeverityDescription"/>
                                                  <xs:enumeration value="AssertionMethod"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:attribute>
                                    </xs:extension>
                                </xs:simpleContent>
                            </xs:complexType>
                        </xs:element>
                        <xs:element maxOccurs="unbounded" minOccurs="0" name="Citation"
                            type="typeCitation"/>
                        <xs:element maxOccurs="unbounded" minOccurs="0" name="XRef" type="typeXref"/>
                        <xs:element maxOccurs="unbounded" minOccurs="0" name="Comment"
                            type="typeComment"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element minOccurs="0" name="ObservedInList">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element maxOccurs="unbounded" name="ObservedIn" type="ObservationSet"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:choice>
                <xs:element maxOccurs="1" minOccurs="1" name="SimpleAllele" type="typeAlleleSCV"> </xs:element>
                <xs:element maxOccurs="1" minOccurs="1" name="Haplotype" type="typeHaplotypeSCV"> </xs:element>
                <xs:element maxOccurs="1" minOccurs="1" name="Genotype" type="typeGenotypeSCV">
                </xs:element>
            </xs:choice>
            <xs:element maxOccurs="1" minOccurs="1" name="TraitSet" type="ClinAsserTraitSetType"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Citation" type="typeCitation"/>
            <xs:element maxOccurs="1" minOccurs="0" name="StudyName" type="xs:string"/>
            <xs:element maxOccurs="1" minOccurs="0" name="StudyDescription" type="xs:string"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Comment" type="typeComment"/>
            <xs:element maxOccurs="1" minOccurs="0" name="SubmissionNameList">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element maxOccurs="unbounded" name="SubmissionName"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
        </xs:sequence>
        <xs:attribute name="DateCreated" type="xs:date">
            <xs:annotation>
                <xs:documentation>               DateCreated is the date when the record first
                    became public in ClinVar.             </xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="DateLastUpdated" type="xs:date">
            <xs:annotation>
                <xs:documentation>The date that the latest update to the submitted record (SCV)
                    became public in ClinVar.             </xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="SubmissionDate" type="xs:date">
            <xs:annotation>
                <xs:documentation>               SubmissionDate is when ClinVar received the
                    submission.             </xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="ID" type="xs:positiveInteger" use="optional"/>
        <xs:attribute name="FDARecognizedDatabase" type="xs:boolean" use="optional"/>
        <xs:attribute name="ContributesToAggregateClassification" type="xs:boolean" use="required"/>
    </xs:complexType>
    <xs:complexType name="ClassificationTypeSCV">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="1" name="ReviewStatus"
                type="typeSubmitterReviewStatusValue"/>
            <xs:choice>
                <xs:element maxOccurs="1" minOccurs="0" name="GermlineClassification">
                    <xs:complexType>
                        <xs:simpleContent>
                            <xs:extension base="xs:string"> </xs:extension>
                        </xs:simpleContent>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="SomaticClinicalImpact">
                    <xs:complexType>
                        <xs:simpleContent>
                            <xs:extension base="xs:string">
                                <xs:attribute name="ClinicalImpactAssertionType" type="xs:string"
                                    use="optional"/>
                                <xs:attribute name="ClinicalImpactClinicalSignificance"
                                    type="xs:string" use="optional"/>
                                <xs:attribute name="DrugForTherapeuticAssertion" type="xs:string"
                                    use="optional"/>
                            </xs:extension>
                        </xs:simpleContent>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="OncogenicityClassification"
                    type="xs:string"/>
            </xs:choice>
            <xs:element maxOccurs="1" minOccurs="0" name="ExplanationOfClassification"
                type="xs:string"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="ClassificationScore">
                <xs:complexType>
                    <xs:simpleContent>
                        <xs:extension base="xs:decimal">
                            <xs:attribute name="type" type="xs:string" use="optional"/>
                        </xs:extension>
                    </xs:simpleContent>
                </xs:complexType>
            </xs:element>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="XRef" type="typeXref"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Citation" type="typeCitation"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Comment" type="typeComment"/>
        </xs:sequence>
        <xs:attribute name="DateLastEvaluated" type="xs:date" use="optional"/>
    </xs:complexType>
    <xs:complexType name="MethodType">
        <xs:annotation>
            <xs:documentation> Details of a method used to generate variant calls or predict/report
                functional consequence. The name of the platform should represent a sequencer or an
                array, e.g. sequencing or array , e.g. capillary, 454, Helicos, Solexa, SOLiD. This
                structure should also be used if the method is 'Curation'. </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="NamePlatform" type="xs:string"/>
            <xs:element maxOccurs="1" minOccurs="0" name="TypePlatform" type="xs:string"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Purpose" type="xs:string"/>
            <xs:element maxOccurs="1" minOccurs="0" name="ResultType">
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                        <xs:enumeration value="number of occurrences"/>
                        <xs:enumeration value="p value"/>
                        <xs:enumeration value="odds ratio"/>
                        <xs:enumeration value="variant call"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="MinReported" type="xs:string"/>
            <xs:element maxOccurs="1" minOccurs="0" name="MaxReported" type="xs:string"/>
            <xs:element maxOccurs="1" minOccurs="0" name="ReferenceStandard" type="xs:string"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Citation" type="typeCitation"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="XRef" type="typeXref"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Description" type="xs:string">
                <xs:annotation>
                    <xs:documentation> Free text to enrich the description of the method and to
                        provide                   information not captured in specific fields.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Software" type="typeSoftwareSet"/>
            <xs:element maxOccurs="1" minOccurs="0" name="SourceType">
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                        <xs:enumeration value="submitter-generated"/>
                        <xs:enumeration value="data mining"/>
                        <xs:enumeration value="data review"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="1" name="MethodType" type="typeMethodlist"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="MethodAttribute">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="Attribute">
                            <xs:complexType>
                                <xs:simpleContent>
                                    <xs:extension base="typeAttribute">
                                        <xs:attribute name="Type" use="required">
                                            <xs:simpleType>
                                                <xs:restriction base="xs:string">
                                                  <xs:enumeration value="Location"/>
                                                  <xs:enumeration value="ControlsAppropriate"/>
                                                  <xs:enumeration value="MethodAppropriate"/>
                                                  <xs:enumeration value="TestName"/>
                                                  <xs:enumeration value="StructVarMethodType"/>
                                                  <xs:enumeration value="ProbeAccession"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:attribute>
                                    </xs:extension>
                                </xs:simpleContent>
                            </xs:complexType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="ObsMethodAttribute">
                <xs:annotation>
                    <xs:documentation> ObsMethodAttribute is used to indicate an attribute specific
                        to a particular                   method in conjunction with a particular
                        observation . </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="Attribute">
                            <xs:complexType>
                                <xs:simpleContent>
                                    <xs:extension base="typeAttribute">
                                        <xs:attribute name="Type" use="required">
                                            <xs:simpleType>
                                                <xs:restriction base="xs:string">
                                                  <xs:enumeration value="MethodResult"/>
                                                  <xs:enumeration value="TestingLaboratory"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:attribute>
                                    </xs:extension>
                                </xs:simpleContent>
                            </xs:complexType>
                        </xs:element>
                        <xs:element maxOccurs="unbounded" minOccurs="0" name="Comment"
                            type="typeComment"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="ObservationSet">
        <xs:annotation>
            <xs:documentation>Documents in what populations or samples an allele or genotype has
                been             observed relative to the described trait. Summary observations can
                be registered per             submitted assertion, grouped by common citation, study
                type, origin, ethnicity, tissue,             cell line, and species data. Not all
                options are valid per study type, but these will             not be validated in the
                xsd. </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="Sample" type="typeSample"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Method">
                <xs:complexType>
                    <xs:complexContent>
                        <xs:extension base="MethodType">
                            <xs:sequence>
                                <xs:element maxOccurs="1" minOccurs="0" name="Type">
                                    <xs:simpleType>
                                        <xs:restriction base="xs:string">
                                            <xs:enumeration value="literature only"/>
                                            <xs:enumeration value="reference population"/>
                                            <xs:enumeration value="case-control"/>
                                            <xs:enumeration value="clinical testing"/>
                                            <xs:enumeration value="in vitro"/>
                                            <xs:enumeration value="in vivo"/>
                                            <xs:enumeration value="inferred from source"/>
                                            <xs:enumeration value="research"/>
                                        </xs:restriction>
                                    </xs:simpleType>
                                </xs:element>
                            </xs:sequence>
                        </xs:extension>
                    </xs:complexContent>
                </xs:complexType>
            </xs:element>
            <xs:element maxOccurs="unbounded" minOccurs="1" name="ObservedData">
                <xs:annotation>
                    <xs:documentation>This is an AttributeSet, there will be 1 attribute supported
                        by                   optional citations, xrefs and comment. There must be at
                        least one ObservedData                   Set, but can be any number. For
                        each ObservedData set the Attribute will be either                   decimal
                        or string depending on type. The value will be stored here, but decimals
                        will be entered to the database as a string. </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="Attribute">
                            <xs:complexType>
                                <xs:simpleContent>
                                    <xs:extension base="typeAttribute">
                                        <xs:attribute name="Type" use="required">
                                            <xs:simpleType>
                                                <xs:restriction base="xs:string">
                                                  <xs:enumeration value="Description"/>
                                                  <xs:enumeration value="VariantAlleles"/>
                                                  <xs:enumeration value="SubjectsWithVariant"/>
                                                  <xs:enumeration
                                                  value="SubjectsWithDifferentCausativeVariant"/>
                                                  <xs:enumeration value="VariantChromosomes"/>
                                                  <!--homozygotes or hemizygotes-->
                                                  <xs:enumeration value="IndependentObservations"/>
                                                  <xs:enumeration value="SingleHeterozygote"/>
                                                  <xs:enumeration value="CompoundHeterozygote"/>
                                                  <xs:enumeration value="Homozygote"/>
                                                  <xs:enumeration value="Hemizygote"/>
                                                  <xs:enumeration value="NumberMosaic"/>
                                                  <xs:enumeration value="ObservedUnspecified"/>
                                                  <xs:enumeration value="AlleleFrequency"/>
                                                  <xs:enumeration value="SecondaryFinding"/>
                                                  <!--subset if family data are available-->
                                                  <xs:enumeration value="GenotypeAndMOIConsistent"/>
                                                  <xs:enumeration
                                                  value="UnaffectedFamilyMemberWithCausativeVariant"/>
                                                  <xs:enumeration
                                                  value="HetParentTransmitNormalAllele"/>
                                                  <xs:enumeration value="CosegregatingFamilies"/>
                                                  <xs:enumeration value="InformativeMeioses"/>
                                                  <xs:enumeration value="SampleLocalID"/>
                                                  <xs:enumeration value="SampleVariantID"/>
                                                  <xs:enumeration value="FamilyHistory"/>
                                                  <xs:enumeration value="NumFamiliesWithVariant"/>
                                                  <xs:enumeration
                                                  value="NumFamiliesWithSegregationObserved"/>
                                                  <xs:enumeration value="SegregationObserved"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:attribute>
                                    </xs:extension>
                                </xs:simpleContent>
                            </xs:complexType>
                        </xs:element>
                        <xs:element maxOccurs="1" minOccurs="0" name="Severity" type="typeSeverity"/>
                        <xs:element maxOccurs="unbounded" minOccurs="0" name="Citation"
                            type="typeCitation"/>
                        <xs:element maxOccurs="unbounded" minOccurs="0" name="XRef" type="typeXref"/>
                        <xs:element maxOccurs="unbounded" minOccurs="0" name="Comment"
                            type="typeComment"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Co-occurrenceSet"
                type="Co-occurrenceType"/>
            <xs:element maxOccurs="1" minOccurs="0" name="TraitSet" type="ClinAsserTraitSetType"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Citation" type="typeCitation"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="XRef" type="typeXref"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Comment" type="typeComment"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="ClinAsserTraitSetType">
        <xs:sequence>
            <xs:element maxOccurs="unbounded" minOccurs="1" name="Trait" type="ClinAsserTraitType"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Name" type="SetElementSetType"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Symbol" type="SetElementSetType"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="AttributeSet">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element maxOccurs="1" minOccurs="1" name="Attribute">
                            <xs:complexType>
                                <xs:simpleContent>
                                    <xs:extension base="typeAttribute">
                                        <xs:attribute name="Type" type="xs:string" use="required"/>
                                    </xs:extension>
                                </xs:simpleContent>
                            </xs:complexType>
                        </xs:element>
                        <xs:element maxOccurs="unbounded" minOccurs="0" name="Citation"
                            type="typeCitation"/>
                        <xs:element maxOccurs="unbounded" minOccurs="0" name="XRef" type="typeXref"/>
                        <xs:element maxOccurs="unbounded" minOccurs="0" name="Comment"
                            type="typeComment"> </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Citation" type="typeCitation"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="XRef" type="typeXref"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Comment" type="typeComment"/>
        </xs:sequence>
        <xs:attribute name="Type" use="required">
            <xs:simpleType>
                <xs:restriction base="xs:string">
                    <xs:enumeration value="Disease"/>
                    <xs:enumeration value="DrugResponse"/>
                    <xs:enumeration value="Finding"/>
                    <xs:enumeration value="PhenotypeInstruction"/>
                    <xs:enumeration value="TraitChoice"/>
                </xs:restriction>
            </xs:simpleType>
        </xs:attribute>
        <xs:attribute name="DateLastEvaluated" type="xs:date"/>
        <xs:attribute name="ID" type="xs:positiveInteger" use="optional"/>
        <xs:attribute name="ContributesToAggregateClassification" type="xs:boolean"/>
        <xs:attribute name="LowerLevelOfEvidence" type="xs:boolean"/>
        <xs:attribute name="multipleConditionExplanation" type="xs:string"/>
    </xs:complexType>
    <xs:simpleType name="ClinicalFeaturesAffectedStatusType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="present"/>
            <xs:enumeration value="absent"/>
            <xs:enumeration value="not tested"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="ClinAsserTraitType">
        <xs:sequence>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Name" type="SetElementSetType"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Symbol" type="SetElementSetType"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="AttributeSet">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element maxOccurs="1" minOccurs="1" name="Attribute">
                            <xs:complexType>
                                <xs:simpleContent>
                                    <xs:extension base="typeAttribute">
                                        <xs:attribute name="Type" type="xs:string" use="required"/>
                                    </xs:extension>
                                </xs:simpleContent>
                            </xs:complexType>
                        </xs:element>
                        <xs:element maxOccurs="unbounded" minOccurs="0" name="Citation"
                            type="typeCitation"/>
                        <xs:element maxOccurs="unbounded" minOccurs="0" name="XRef" type="typeXref"/>
                        <xs:element maxOccurs="unbounded" minOccurs="0" name="Comment"
                            type="typeComment"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="TraitRelationship">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element maxOccurs="unbounded" minOccurs="0" name="Name"
                            type="SetElementSetType"/>
                        <xs:element maxOccurs="unbounded" minOccurs="0" name="Symbol"
                            type="SetElementSetType"/>
                        <xs:element maxOccurs="unbounded" minOccurs="0" name="AttributeSet">
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element maxOccurs="1" minOccurs="1" name="Attribute">
                                        <xs:complexType>
                                            <xs:simpleContent>
                                                <xs:extension base="typeAttribute">
                                                  <xs:attribute name="Type" type="xs:string"
                                                  use="required"/>
                                                </xs:extension>
                                            </xs:simpleContent>
                                        </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="unbounded" minOccurs="0" name="Citation"
                                        type="typeCitation"/>
                                    <xs:element maxOccurs="unbounded" minOccurs="0" name="XRef"
                                        type="typeXref"/>
                                    <xs:element maxOccurs="unbounded" minOccurs="0" name="Comment"
                                        type="typeComment"/>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                        <xs:element maxOccurs="unbounded" minOccurs="0" name="Citation"
                            type="typeCitation"/>
                        <xs:element maxOccurs="unbounded" minOccurs="0" name="XRef" type="typeXref"/>
                        <xs:element maxOccurs="unbounded" minOccurs="0" name="Source"
                            type="xs:string"/>
                    </xs:sequence>
                    <xs:attribute name="Type" use="required">
                        <xs:simpleType>
                            <xs:restriction base="xs:string">
                                <xs:enumeration value="phenocopy"/>
                                <xs:enumeration value="Subphenotype"/>
                                <xs:enumeration value="DrugResponseAndDisease"/>
                                <xs:enumeration value="co-occurring condition"/>
                                <xs:enumeration value="Finding member"/>
                            </xs:restriction>
                        </xs:simpleType>
                    </xs:attribute>
                </xs:complexType>
            </xs:element>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Citation" type="typeCitation"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="XRef" type="typeXref"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Comment" type="typeComment"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Source" type="xs:string"/>
        </xs:sequence>
        <xs:attribute name="Type" use="required">
            <xs:simpleType>
                <xs:restriction base="xs:string">
                    <xs:enumeration value="Disease"/>
                    <xs:enumeration value="DrugResponse"/>
                    <xs:enumeration value="BloodGroup"/>
                    <xs:enumeration value="Finding"/>
                    <xs:enumeration value="NamedProteinVariant"/>
                    <xs:enumeration value="PhenotypeInstruction"/>
                </xs:restriction>
            </xs:simpleType>
        </xs:attribute>
        <xs:attribute name="ClinicalFeaturesAffectedStatus"
            type="ClinicalFeaturesAffectedStatusType" use="optional"/>
        <xs:attribute name="ID" type="xs:positiveInteger" use="optional"/>
    </xs:complexType>
    <xs:complexType name="SetElementSetType">
        <xs:sequence>
            <xs:element name="ElementValue">
                <xs:complexType>
                    <xs:simpleContent>
                        <xs:extension base="xs:string">
                            <xs:attribute name="Type" type="xs:string" use="required"/>
                        </xs:extension>
                    </xs:simpleContent>
                </xs:complexType>
            </xs:element>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Citation" type="typeCitation"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="XRef" type="typeXref"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Comment" type="typeComment"/>
        </xs:sequence>
    </xs:complexType>
    <xs:attributeGroup name="SubmitterIdentifiers">
        <xs:annotation>
            <xs:documentation>Set of attributes for the primary submitter. Any addtional submitters
                are             captured in the AdditionalSubmitters element</xs:documentation>
        </xs:annotation>
        <xs:attribute name="SubmitterName" type="xs:string" use="required"/>
        <xs:attribute name="OrgID" type="xs:positiveInteger" use="required"/>
        <xs:attribute name="OrganizationCategory" type="xs:string" use="required"/>
        <xs:attribute name="OrgAbbreviation" type="xs:string" use="optional"/>
    </xs:attributeGroup>
    <xs:complexType name="typeAttributeSet">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="1" name="Attribute">
                <xs:complexType>
                    <xs:simpleContent>
                        <xs:extension base="typeAttribute">
                            <xs:attribute name="Type" type="xs:string" use="required"/>
                        </xs:extension>
                    </xs:simpleContent>
                </xs:complexType>
            </xs:element>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Citation" type="typeCitation"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="XRef" type="typeXref"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Comment" type="typeComment"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="IndicationType">
        <xs:sequence>
            <xs:element maxOccurs="unbounded" minOccurs="1" name="Trait" type="ClinAsserTraitType">
                <xs:annotation>
                    <xs:documentation>Represents the value for the test indication as a name of a
                        trait.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Name" type="SetElementSetType"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Symbol" type="SetElementSetType"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="AttributeSet"
                type="typeAttributeSet"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Citation" type="typeCitation"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="XRef" type="typeXref"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Comment" type="typeComment"/>
        </xs:sequence>
        <xs:attribute name="Type" use="required">
            <xs:simpleType>
                <xs:restriction base="xs:string">
                    <xs:enumeration value="Indication"/>
                </xs:restriction>
            </xs:simpleType>
        </xs:attribute>
        <xs:attribute name="ID" type="xs:positiveInteger" use="optional"/>
    </xs:complexType>
    <xs:complexType name="ClinicalSignificanceType">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="ReviewStatus"
                type="typeSubmitterReviewStatusValue"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Description" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Structure used to support old data of AlleleDescriptionSet within
                        Co-occurenceSet. </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Explanation" type="typeComment">
                <xs:annotation>
                    <xs:documentation>Explanation is used only when the description is 'conflicting
                        data from submitters'             The element summarizes the
                        conflict.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="XRef" type="typeXref"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Citation" type="typeCitation"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Comment" type="typeComment"/>
        </xs:sequence>
        <xs:attribute name="DateLastEvaluated" type="xs:date" use="optional"/>
    </xs:complexType>
    <xs:complexType name="typeRecordHistory">
        <xs:annotation>
            <xs:documentation>A structure to support reporting of an accession, its version, the
                date its status changed, and          text describing that
                change.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Comment" type="typeComment"/>
        </xs:sequence>
        <xs:attribute name="Accession" type="xs:string" use="required"/>
        <xs:attribute name="Version" type="xs:nonNegativeInteger" use="required"/>
        <xs:attribute name="DateChanged" type="xs:date" use="required"/>
        <xs:attribute name="VariationID" type="xs:nonNegativeInteger" use="optional">
            <xs:annotation>
                <xs:documentation>Attribute @VaritionID is only populated for VCV, where @Accession
                    is like VCV000000009</xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>
    <xs:complexType name="typeGenotypeSCV">
        <xs:annotation>
            <xs:documentation>Used to report genotypes, be they simple or complex
                diplotypes.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:choice maxOccurs="unbounded" minOccurs="1">
                <xs:element maxOccurs="1" minOccurs="1" name="SimpleAllele" type="typeAlleleSCV"> </xs:element>
                <xs:element maxOccurs="1" minOccurs="1" name="Haplotype" type="typeHaplotypeSCV">
                    <xs:annotation>
                        <xs:documentation>Allows more than 2 haplotypes per genotype to support
                            representation                     of ploidy.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:choice>
            <xs:element maxOccurs="1" minOccurs="0" name="Name" type="xs:string"/>
            <xs:element minOccurs="0" name="OtherNameList" type="typeNames">
                <xs:annotation>
                    <xs:documentation>Other names used for the Genotype</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="1" name="VariationType" type="typeVariationType"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="FunctionalConsequence">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element maxOccurs="unbounded" minOccurs="0" name="XRef" type="typeXref"/>
                        <xs:element maxOccurs="unbounded" minOccurs="0" name="Citation"
                            type="typeCitation"/>
                        <xs:element maxOccurs="unbounded" minOccurs="0" name="Comment"
                            type="typeComment"/>
                    </xs:sequence>
                    <xs:attribute name="Value" type="xs:string" use="required"/>
                </xs:complexType>
            </xs:element>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="AttributeSet"
                type="typeAttributeSet"/>
            <xs:element minOccurs="0" name="CitationList">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element maxOccurs="unbounded" name="Citation" type="typeCitation"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element minOccurs="0" name="XRefList">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element maxOccurs="unbounded" minOccurs="0" name="XRef" type="typeXref"
                        />
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Comment" type="typeComment"/>
        </xs:sequence>
        <xs:attribute name="VariationID" type="xs:int" use="optional"/>
    </xs:complexType>
    <xs:simpleType name="typeHaplotypeVariationType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="Haplotype"/>
            <xs:enumeration value="Haplotype, single variant"/>
            <xs:enumeration value="Variation"/>
            <xs:enumeration value="Phase unknown"/>
            <xs:enumeration value="Haplotype defined by a single variant"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="typeRCVInterpretedCondition">
        <xs:simpleContent>
            <xs:extension base="xs:string">
                <xs:attribute name="DB" type="xs:string" use="optional"/>
                <xs:attribute name="ID" type="xs:string" use="optional"/>
            </xs:extension>
        </xs:simpleContent>
    </xs:complexType>
    <xs:complexType name="typeClinicalAssertionRecordHistory">
        <xs:annotation>
            <xs:documentation>inside ClinicalAssertion, A structure to support reporting of an
                accession, its version, the date its status changed, and          text describing
                that change.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Comment" type="typeComment"/>
        </xs:sequence>
        <xs:attribute name="Accession" type="xs:string" use="required"/>
        <xs:attribute name="Version" type="xs:nonNegativeInteger" use="optional"/>
        <xs:attribute name="DateChanged" type="xs:date" use="required"/>
    </xs:complexType>
</xs:schema>
