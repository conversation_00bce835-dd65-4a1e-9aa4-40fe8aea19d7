import sqlite3
from datetime import datetime
from typing import Optional, List, Dict, Any

class ClinVarDatabase:
    def __init__(self, db_path: str = "clinvar.db"):
        self.db_path = db_path
        self.connection = None
    
    def connect(self):
        """Conectar a la base de datos SQLite"""
        self.connection = sqlite3.connect(self.db_path)
        self.connection.row_factory = sqlite3.Row  # Para acceder a columnas por nombre
        return self.connection
    
    def close(self):
        """Cerrar conexión a la base de datos"""
        if self.connection:
            self.connection.close()
    
    def initialize_database(self):
        """Crear las tablas necesarias"""
        print("🔧 Inicializando base de datos...")
        
        cursor = self.connection.cursor()
        
        # Tabla principal para variantes
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS variants (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                vcv_id TEXT UNIQUE NOT NULL,
                title TEXT,
                species TEXT,
                variation_type TEXT,
                date_created TEXT,
                date_last_updated TEXT,
                review_status TEXT,
                clinical_significance TEXT,
                interpretation_description TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Tabla para genes
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS genes (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                variant_id INTEGER,
                gene_id TEXT,
                gene_symbol TEXT,
                hgnc_id TEXT,
                full_name TEXT,
                FOREIGN KEY (variant_id) REFERENCES variants (id)
            )
        """)
        
        # Tabla para condiciones
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS conditions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                variant_id INTEGER,
                condition_name TEXT,
                condition_id TEXT,
                db_name TEXT,
                FOREIGN KEY (variant_id) REFERENCES variants (id)
            )
        """)
        
        # Tabla para ubicaciones genómicas
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS genomic_locations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                variant_id INTEGER,
                chromosome TEXT,
                start_position INTEGER,
                stop_position INTEGER,
                reference_allele TEXT,
                alternate_allele TEXT,
                assembly TEXT,
                FOREIGN KEY (variant_id) REFERENCES variants (id)
            )
        """)
        
        # Tabla para submitters
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS submitters (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                variant_id INTEGER,
                submitter_name TEXT,
                submission_date TEXT,
                FOREIGN KEY (variant_id) REFERENCES variants (id)
            )
        """)
        
        self.connection.commit()
        print("✅ Base de datos inicializada correctamente")
    
    def insert_variant(self, variant_data: Dict[str, Any]) -> int:
        """Insertar una variante y retornar su ID"""
        cursor = self.connection.cursor()
        
        cursor.execute("""
            INSERT OR REPLACE INTO variants
            (vcv_id, title, species, variation_type, date_created, date_last_updated,
             review_status, clinical_significance, interpretation_description)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            variant_data.get('vcv_id'),
            variant_data.get('title'),
            variant_data.get('species'),
            variant_data.get('variation_type'),
            variant_data.get('date_created'),
            variant_data.get('date_last_updated'),
            variant_data.get('review_status'),
            variant_data.get('clinical_significance'),
            variant_data.get('interpretation_description')
        ))
        
        self.connection.commit()
        return cursor.lastrowid
    
    def insert_gene(self, variant_id: int, gene_data: Dict[str, Any]):
        """Insertar información de un gen"""
        cursor = self.connection.cursor()
        
        cursor.execute("""
            INSERT INTO genes (variant_id, gene_id, gene_symbol, hgnc_id, full_name)
            VALUES (?, ?, ?, ?, ?)
        """, (
            variant_id,
            gene_data.get('gene_id'),
            gene_data.get('gene_symbol'),
            gene_data.get('hgnc_id'),
            gene_data.get('full_name')
        ))
        
        self.connection.commit()
    
    def insert_condition(self, variant_id: int, condition_data: Dict[str, Any]):
        """Insertar información de una condición"""
        cursor = self.connection.cursor()
        
        cursor.execute("""
            INSERT INTO conditions (variant_id, condition_name, condition_id, db_name)
            VALUES (?, ?, ?, ?)
        """, (
            variant_id,
            condition_data.get('condition_name'),
            condition_data.get('condition_id'),
            condition_data.get('db_name')
        ))
        
        self.connection.commit()
    
    def insert_genomic_location(self, variant_id: int, location_data: Dict[str, Any]):
        """Insertar información de ubicación genómica"""
        cursor = self.connection.cursor()
        
        cursor.execute("""
            INSERT INTO genomic_locations 
            (variant_id, chromosome, start_position, stop_position, 
             reference_allele, alternate_allele, assembly)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (
            variant_id,
            location_data.get('chromosome'),
            location_data.get('start_position'),
            location_data.get('stop_position'),
            location_data.get('reference_allele'),
            location_data.get('alternate_allele'),
            location_data.get('assembly')
        ))
        
        self.connection.commit()
    
    def insert_submitter(self, variant_id: int, submitter_data: Dict[str, Any]):
        """Insertar información de submitter"""
        cursor = self.connection.cursor()
        
        cursor.execute("""
            INSERT INTO submitters (variant_id, submitter_name, submission_date)
            VALUES (?, ?, ?)
        """, (
            variant_id,
            submitter_data.get('submitter_name'),
            submitter_data.get('submission_date')
        ))
        
        self.connection.commit()
    
    def get_variant_count(self) -> int:
        """Obtener el número total de variantes"""
        cursor = self.connection.cursor()
        cursor.execute("SELECT COUNT(*) FROM variants")
        return cursor.fetchone()[0]
    
    def get_variants(self, limit: int = 10, offset: int = 0) -> List[sqlite3.Row]:
        """Obtener variantes con paginación"""
        cursor = self.connection.cursor()
        cursor.execute("""
            SELECT * FROM variants 
            ORDER BY date_last_updated DESC 
            LIMIT ? OFFSET ?
        """, (limit, offset))
        return cursor.fetchall()
    
    def get_variant_details(self, vcv_id: str) -> Dict[str, Any]:
        """Obtener detalles completos de una variante"""
        cursor = self.connection.cursor()
        
        # Obtener variante
        cursor.execute("SELECT * FROM variants WHERE vcv_id = ?", (vcv_id,))
        variant = cursor.fetchone()
        
        if not variant:
            return None
        
        variant_dict = dict(variant)
        variant_id = variant['id']
        
        # Obtener genes
        cursor.execute("SELECT * FROM genes WHERE variant_id = ?", (variant_id,))
        variant_dict['genes'] = [dict(row) for row in cursor.fetchall()]
        
        # Obtener condiciones
        cursor.execute("SELECT * FROM conditions WHERE variant_id = ?", (variant_id,))
        variant_dict['conditions'] = [dict(row) for row in cursor.fetchall()]
        
        # Obtener ubicaciones genómicas
        cursor.execute("SELECT * FROM genomic_locations WHERE variant_id = ?", (variant_id,))
        variant_dict['genomic_locations'] = [dict(row) for row in cursor.fetchall()]
        
        # Obtener submitters
        cursor.execute("SELECT * FROM submitters WHERE variant_id = ?", (variant_id,))
        variant_dict['submitters'] = [dict(row) for row in cursor.fetchall()]
        
        return variant_dict
