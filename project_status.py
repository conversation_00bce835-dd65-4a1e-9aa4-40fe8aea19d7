#!/usr/bin/env python3
"""
Script para mostrar el estado actual del proyecto ClinVar
"""

import os
import sys

def check_files():
    """Verificar que todos los archivos necesarios estén presentes"""
    required_files = [
        'clinvar_main.py',
        'process_sample.py', 
        'query_clinvar.py',
        'database.py',
        'xml_parser.py',
        'test_parser.py',
        'debug_xml.py',
        'README.md'
    ]
    
    optional_files = [
        'part_000.xml',
        'clinvar_sample.db',
        'clinvar.db'
    ]
    
    print("📁 Estado de archivos del proyecto")
    print("=" * 40)
    
    print("\n✅ Archivos principales:")
    for file in required_files:
        if os.path.exists(file):
            size = os.path.getsize(file)
            print(f"   ✓ {file} ({size:,} bytes)")
        else:
            print(f"   ❌ {file} - FALTANTE")
    
    print("\n📊 Archivos de datos:")
    for file in optional_files:
        if os.path.exists(file):
            size = os.path.getsize(file)
            if file.endswith('.xml'):
                print(f"   ✓ {file} ({size/1024/1024:.1f} MB)")
            else:
                print(f"   ✓ {file} ({size:,} bytes)")
        else:
            print(f"   - {file} - No presente")

def check_dependencies():
    """Verificar dependencias de Python"""
    print("\n🐍 Dependencias de Python")
    print("=" * 30)
    
    try:
        import xml.etree.ElementTree as ET
        print("   ✓ xml.etree.ElementTree - Disponible")
    except ImportError:
        print("   ❌ xml.etree.ElementTree - No disponible")
    
    try:
        import sqlite3
        print("   ✓ sqlite3 - Disponible")
    except ImportError:
        print("   ❌ sqlite3 - No disponible")
    
    try:
        import lxml
        print("   ✓ lxml - Disponible")
    except ImportError:
        print("   ⚠️  lxml - No disponible (instalar con: pip install lxml)")

def show_database_status():
    """Mostrar estado de las bases de datos"""
    print("\n🗄️  Estado de bases de datos")
    print("=" * 35)
    
    databases = ['clinvar_sample.db', 'clinvar.db']
    
    for db_file in databases:
        if os.path.exists(db_file):
            try:
                import sqlite3
                conn = sqlite3.connect(db_file)
                cursor = conn.cursor()
                
                # Contar variantes
                cursor.execute("SELECT COUNT(*) FROM variants")
                variant_count = cursor.fetchone()[0]
                
                # Obtener fecha de última actualización
                cursor.execute("SELECT MAX(created_at) FROM variants")
                last_update = cursor.fetchone()[0]
                
                size = os.path.getsize(db_file)
                print(f"   ✓ {db_file}")
                print(f"     📊 Variantes: {variant_count:,}")
                print(f"     📏 Tamaño: {size:,} bytes")
                print(f"     📅 Última actualización: {last_update or 'No disponible'}")
                
                conn.close()
                
            except Exception as e:
                print(f"   ⚠️  {db_file} - Error leyendo: {e}")
        else:
            print(f"   - {db_file} - No existe")

def show_usage_examples():
    """Mostrar ejemplos de uso"""
    print("\n🚀 Ejemplos de uso rápido")
    print("=" * 30)
    
    print("\n1. Procesar muestra pequeña (recomendado para empezar):")
    print("   python process_sample.py")
    
    print("\n2. Ver estadísticas de datos procesados:")
    print("   python query_clinvar.py --summary")
    
    print("\n3. Buscar variantes por gen:")
    print("   python query_clinvar.py --gene VWF")
    
    print("\n4. Ver detalles de variante específica:")
    print("   python query_clinvar.py --variant VCV000000003")
    
    print("\n5. Procesar archivo completo:")
    print("   python clinvar_main.py --file part_000.xml")
    
    print("\n6. Procesar número limitado de variantes:")
    print("   python clinvar_main.py --limit 1000")

def show_next_steps():
    """Mostrar próximos pasos recomendados"""
    print("\n📋 Próximos pasos recomendados")
    print("=" * 35)
    
    if not os.path.exists('part_000.xml'):
        print("   1. ⬇️  Descargar archivo XML de ClinVar")
        print("      - Visita: https://ftp.ncbi.nlm.nih.gov/pub/clinvar/xml/")
        print("      - Descarga el archivo XML más reciente")
        print("      - Renómbralo como 'part_000.xml'")
    
    if not os.path.exists('clinvar_sample.db'):
        print("   2. 🧪 Ejecutar procesamiento de muestra:")
        print("      python process_sample.py")
    
    if os.path.exists('clinvar_sample.db'):
        print("   3. 🔍 Explorar los datos procesados:")
        print("      python query_clinvar.py --summary")
        print("      python query_clinvar.py --gene BRCA1")
    
    if os.path.exists('part_000.xml') and os.path.exists('clinvar_sample.db'):
        print("   4. 🚀 Procesar más variantes:")
        print("      python clinvar_main.py --limit 10000")
    
    print("\n   5. 📖 Leer documentación completa:")
    print("      cat README.md")

def main():
    print("🧬 ClinVar XML Processor - Estado del Proyecto")
    print("=" * 50)
    print(f"📍 Directorio actual: {os.getcwd()}")
    print(f"🐍 Python: {sys.version}")
    
    check_files()
    check_dependencies()
    show_database_status()
    show_usage_examples()
    show_next_steps()
    
    print("\n" + "=" * 50)
    print("✅ Verificación de estado completada")

if __name__ == "__main__":
    main()
