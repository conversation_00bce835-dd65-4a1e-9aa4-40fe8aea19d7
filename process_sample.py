#!/usr/bin/env python3
"""
Procesador de muestra para procesar solo las primeras N variantes
"""

from database import ClinVarDatabase
from xml_parser import ClinVarXMLParser
import os
import time

def process_sample_variants(num_variants=100):
    """Procesar solo las primeras N variantes"""
    print(f"🧬 Procesando las primeras {num_variants} variantes de ClinVar")
    print("=" * 60)
    
    xml_file = "part_000.xml"
    if not os.path.exists(xml_file):
        print(f"❌ Archivo no encontrado: {xml_file}")
        return
    
    # Inicializar componentes
    db = ClinVarDatabase("clinvar_sample.db")
    parser = ClinVarXMLParser()
    
    processed_count = 0
    error_count = 0
    start_time = time.time()
    
    try:
        # Conectar a la base de datos
        db.connect()
        db.initialize_database()
        
        # Parsear XML
        root = parser.parse_file(xml_file)
        variation_archives = parser.get_variation_archives(root)
        
        print(f"📊 Total de variantes en el archivo: {len(variation_archives)}")
        print(f"🎯 Procesando las primeras {num_variants} variantes...\n")
        
        # Procesar las primeras N variantes
        for i, variant_archive in enumerate(variation_archives[:num_variants], 1):
            try:
                # Extraer datos de la variante
                variant_data = parser.extract_variant_data(variant_archive)
                if not variant_data or not variant_data.get('vcv_id'):
                    print(f"⚠️  Variante {i}: Sin datos válidos, saltando...")
                    error_count += 1
                    continue
                
                vcv_id = variant_data['vcv_id']
                
                # Insertar variante principal
                variant_id = db.insert_variant(variant_data)
                
                # Extraer e insertar datos relacionados
                genes = parser.extract_genes(variant_archive)
                for gene in genes:
                    db.insert_gene(variant_id, gene)
                
                conditions = parser.extract_conditions(variant_archive)
                for condition in conditions:
                    db.insert_condition(variant_id, condition)
                
                locations = parser.extract_genomic_locations(variant_archive)
                for location in locations:
                    db.insert_genomic_location(variant_id, location)
                
                submitters = parser.extract_submitters(variant_archive)
                for submitter in submitters:
                    db.insert_submitter(variant_id, submitter)
                
                processed_count += 1
                
                # Mostrar progreso cada 10 variantes
                if i % 10 == 0:
                    elapsed = time.time() - start_time
                    rate = i / elapsed
                    print(f"⏳ Progreso: {i}/{num_variants} ({i/num_variants*100:.1f}%) - "
                          f"Velocidad: {rate:.1f} var/seg")
                
                # Mostrar detalles de las primeras 5 variantes
                if i <= 5:
                    print(f"✅ Variante {i}: {vcv_id} - {variant_data.get('title', 'Sin título')[:50]}...")
                    print(f"   📊 Genes: {len(genes)}, Condiciones: {len(conditions)}, "
                          f"Ubicaciones: {len(locations)}, Submitters: {len(submitters)}")
                
            except Exception as e:
                print(f"❌ Error procesando variante {i}: {e}")
                error_count += 1
        
        # Mostrar estadísticas finales
        elapsed_time = time.time() - start_time
        print(f"\n✅ Procesamiento completado!")
        print(f"📊 Variantes procesadas: {processed_count}")
        print(f"❌ Errores: {error_count}")
        print(f"⏱️  Tiempo total: {elapsed_time:.2f} segundos")
        print(f"🚀 Velocidad promedio: {processed_count/elapsed_time:.2f} variantes/segundo")
        
        # Mostrar estadísticas de la base de datos
        show_database_stats(db)
        
    except Exception as e:
        print(f"❌ Error en el procesamiento: {e}")
    finally:
        db.close()

def show_database_stats(db):
    """Mostrar estadísticas de la base de datos"""
    print(f"\n📊 Estadísticas de la base de datos")
    print("=" * 40)
    
    try:
        variant_count = db.get_variant_count()
        print(f"🧬 Total de variantes: {variant_count}")
        
        if variant_count > 0:
            print(f"\n📋 Últimas 5 variantes procesadas:")
            print("-" * 40)
            
            recent_variants = db.get_variants(5, 0)
            for i, variant in enumerate(recent_variants, 1):
                print(f"{i}. {variant['vcv_id']}")
                print(f"   📝 {variant['title'][:60]}...")
                print(f"   🔬 Tipo: {variant['variation_type'] or 'No especificado'}")
                print(f"   🏥 Significancia: {variant['clinical_significance'] or 'No especificada'}")
                print(f"   📅 Actualizada: {variant['date_last_updated'] or 'No especificada'}")
                print()
                
            # Mostrar ejemplo de variante completa
            if recent_variants:
                print(f"🔍 Detalles completos de la primera variante:")
                first_variant = db.get_variant_details(recent_variants[0]['vcv_id'])
                if first_variant:
                    print(f"   📊 Genes asociados: {len(first_variant['genes'])}")
                    print(f"   🏥 Condiciones: {len(first_variant['conditions'])}")
                    print(f"   📍 Ubicaciones genómicas: {len(first_variant['genomic_locations'])}")
                    print(f"   👥 Submitters: {len(first_variant['submitters'])}")
                    
                    if first_variant['genes']:
                        print(f"   🧬 Primer gen: {first_variant['genes'][0]['gene_symbol']} "
                              f"({first_variant['genes'][0]['full_name'] or 'Sin nombre completo'})")
                    
                    if first_variant['conditions']:
                        print(f"   🏥 Primera condición: {first_variant['conditions'][0]['condition_name']}")
                        
    except Exception as e:
        print(f"❌ Error mostrando estadísticas: {e}")

def main():
    # Procesar una muestra pequeña primero
    process_sample_variants(100)
    
    print(f"\n🎉 ¡Procesamiento de muestra completado!")
    print(f"📁 Base de datos guardada como: clinvar_sample.db")
    print(f"\n💡 Para procesar todas las variantes, ejecuta: python clinvar_processor.py")

if __name__ == "__main__":
    main()
