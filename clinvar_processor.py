#!/usr/bin/env python3
"""
ClinVar XML Processor
Procesa archivos XML de ClinVar y los almacena en una base de datos SQLite
"""

import os
import sys
import time
from typing import Optional
from database import ClinVarDatabase
from xml_parser import ClinVarXMLParser

class ClinVarProcessor:
    def __init__(self, db_path: str = "clinvar.db"):
        self.db = ClinVarDatabase(db_path)
        self.parser = ClinVarXMLParser()
        self.processed_count = 0
        self.error_count = 0
        self.start_time = None
    
    def initialize(self):
        """Inicializar base de datos y conexión"""
        print("🧬 ClinVar XML Processor")
        print("=" * 50)
        
        self.db.connect()
        self.db.initialize_database()
        self.start_time = time.time()
    
    def process_xml_file(self, file_path: str):
        """Procesar un archivo XML de ClinVar"""
        print(f"\n📁 Procesando archivo: {file_path}")
        
        if not os.path.exists(file_path):
            print(f"❌ Archivo no encontrado: {file_path}")
            return
        
        try:
            # Parsear XML
            root = self.parser.parse_file(file_path)
            
            # Obtener todas las variantes
            variation_archives = self.parser.get_variation_archives(root)
            total_variants = len(variation_archives)
            
            print(f"🔍 Encontradas {total_variants} variantes para procesar")
            
            if total_variants == 0:
                print("⚠️  No se encontraron variantes en el archivo")
                return
            
            # Procesar cada variante
            for i, variant_archive in enumerate(variation_archives, 1):
                self.process_variant(variant_archive, i, total_variants)
                
                # Mostrar progreso cada 50 variantes
                if i % 50 == 0:
                    elapsed = time.time() - self.start_time
                    rate = i / elapsed
                    eta = (total_variants - i) / rate if rate > 0 else 0
                    print(f"⏳ Progreso: {i}/{total_variants} ({i/total_variants*100:.1f}%) - "
                          f"Velocidad: {rate:.1f} var/seg - ETA: {eta:.0f}s")
            
            print(f"\n✅ Procesamiento completado!")
            print(f"📊 Variantes procesadas: {self.processed_count}")
            print(f"❌ Errores: {self.error_count}")
            
        except Exception as e:
            print(f"❌ Error procesando archivo XML: {e}")
            self.error_count += 1
    
    def process_variant(self, variation_archive, current: int, total: int):
        """Procesar una variante individual"""
        try:
            # Extraer datos de la variante
            variant_data = self.parser.extract_variant_data(variation_archive)
            if not variant_data or not variant_data.get('vcv_id'):
                print(f"⚠️  Variante {current}/{total}: Sin datos válidos, saltando...")
                self.error_count += 1
                return
            
            vcv_id = variant_data['vcv_id']
            
            # Insertar variante principal
            variant_id = self.db.insert_variant(variant_data)
            
            # Extraer e insertar datos relacionados
            genes = self.parser.extract_genes(variation_archive)
            for gene in genes:
                self.db.insert_gene(variant_id, gene)
            
            conditions = self.parser.extract_conditions(variation_archive)
            for condition in conditions:
                self.db.insert_condition(variant_id, condition)
            
            locations = self.parser.extract_genomic_locations(variation_archive)
            for location in locations:
                self.db.insert_genomic_location(variant_id, location)
            
            submitters = self.parser.extract_submitters(variation_archive)
            for submitter in submitters:
                self.db.insert_submitter(variant_id, submitter)
            
            self.processed_count += 1
            
            # Log detallado para las primeras 10 variantes
            if current <= 10:
                print(f"✅ Variante {current}/{total}: {vcv_id} - {variant_data.get('title', 'Sin título')[:50]}...")
            
        except Exception as e:
            print(f"❌ Error procesando variante {current}: {e}")
            self.error_count += 1
    
    def show_statistics(self):
        """Mostrar estadísticas de la base de datos"""
        print("\n📊 Estadísticas de la base de datos")
        print("=" * 40)
        
        try:
            variant_count = self.db.get_variant_count()
            print(f"🧬 Total de variantes: {variant_count}")
            
            if variant_count > 0:
                print(f"\n📋 Últimas 5 variantes procesadas:")
                print("-" * 40)
                
                recent_variants = self.db.get_variants(5, 0)
                for i, variant in enumerate(recent_variants, 1):
                    print(f"{i}. {variant['vcv_id']}")
                    print(f"   📝 {variant['title'][:60]}...")
                    print(f"   🔬 Significancia: {variant['clinical_significance'] or 'No especificada'}")
                    print(f"   📅 Actualizada: {variant['date_last_updated'] or 'No especificada'}")
                    print()
            
            # Tiempo total de procesamiento
            if self.start_time:
                total_time = time.time() - self.start_time
                print(f"⏱️  Tiempo total de procesamiento: {total_time:.2f} segundos")
                if self.processed_count > 0:
                    print(f"🚀 Velocidad promedio: {self.processed_count/total_time:.2f} variantes/segundo")
                    
        except Exception as e:
            print(f"❌ Error mostrando estadísticas: {e}")
    
    def search_variant(self, vcv_id: str):
        """Buscar una variante específica"""
        print(f"\n🔍 Buscando variante: {vcv_id}")
        
        try:
            variant = self.db.get_variant_details(vcv_id)
            if variant:
                print(f"✅ Variante encontrada:")
                print(f"   ID: {variant['vcv_id']}")
                print(f"   Título: {variant['title']}")
                print(f"   Significancia clínica: {variant['clinical_significance']}")
                print(f"   Estado de revisión: {variant['review_status']}")
                print(f"   Genes asociados: {len(variant['genes'])}")
                print(f"   Condiciones: {len(variant['conditions'])}")
                print(f"   Ubicaciones genómicas: {len(variant['genomic_locations'])}")
            else:
                print(f"❌ Variante {vcv_id} no encontrada")
        except Exception as e:
            print(f"❌ Error buscando variante: {e}")
    
    def close(self):
        """Cerrar conexiones"""
        self.db.close()

def main():
    """Función principal"""
    processor = ClinVarProcessor()
    
    try:
        # Inicializar
        processor.initialize()
        
        # Verificar si existe el archivo XML
        xml_file = "part_000.xml"
        if not os.path.exists(xml_file):
            print(f"❌ Archivo XML no encontrado: {xml_file}")
            print("📁 Archivos disponibles en el directorio actual:")
            for file in os.listdir("."):
                if file.endswith(".xml"):
                    print(f"   - {file}")
            return
        
        # Procesar archivo XML
        processor.process_xml_file(xml_file)
        
        # Mostrar estadísticas
        processor.show_statistics()
        
        # Ejemplo de búsqueda (si hay variantes)
        variant_count = processor.db.get_variant_count()
        if variant_count > 0:
            # Obtener la primera variante para mostrar ejemplo
            variants = processor.db.get_variants(1, 0)
            if variants:
                example_vcv = variants[0]['vcv_id']
                processor.search_variant(example_vcv)
        
    except KeyboardInterrupt:
        print("\n⏹️  Procesamiento interrumpido por el usuario")
    except Exception as e:
        print(f"❌ Error en el procesamiento: {e}")
    finally:
        processor.close()
        print("\n👋 Procesamiento finalizado")

if __name__ == "__main__":
    main()
