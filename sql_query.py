#!/usr/bin/env python3
"""
Consultas SQL directas a la base de datos ClinVar
"""

import sqlite3
import os

def execute_sql_query(db_path, query, description):
    """Ejecutar una consulta SQL y mostrar resultados"""
    print(f"\n🔍 {description}")
    print("-" * 50)
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        cursor.execute(query)
        results = cursor.fetchall()
        
        if not results:
            print("   No se encontraron resultados")
            return
        
        # Obtener nombres de columnas
        column_names = [description[0] for description in cursor.description]
        
        # Mostrar resultados
        for i, row in enumerate(results, 1):
            print(f"   {i}. ", end="")
            for j, value in enumerate(row):
                if j > 0:
                    print(" | ", end="")
                print(f"{column_names[j]}: {value}", end="")
            print()
        
        conn.close()
        
    except Exception as e:
        print(f"   ❌ Error ejecutando consulta: {e}")

def main():
    """Ejecutar varias consultas interesantes"""
    db_path = "clinvar_sample.db"
    
    if not os.path.exists(db_path):
        print(f"❌ Base de datos no encontrada: {db_path}")
        return
    
    print("🧬 Consultas SQL Directas - Base de Datos ClinVar")
    print("=" * 60)
    
    # 1. Variantes con más genes asociados
    query1 = """
    SELECT v.vcv_id, v.title, COUNT(g.id) as gene_count
    FROM variants v
    LEFT JOIN genes g ON v.id = g.variant_id
    GROUP BY v.id
    ORDER BY gene_count DESC
    LIMIT 5
    """
    execute_sql_query(db_path, query1, "Variantes con más genes asociados")
    
    # 2. Genes con más variantes
    query2 = """
    SELECT g.gene_symbol, g.full_name, COUNT(DISTINCT v.id) as variant_count
    FROM genes g
    JOIN variants v ON g.variant_id = v.id
    WHERE g.gene_symbol IS NOT NULL
    GROUP BY g.gene_symbol, g.full_name
    ORDER BY variant_count DESC
    LIMIT 5
    """
    execute_sql_query(db_path, query2, "Genes con más variantes asociadas")
    
    # 3. Variantes más recientes
    query3 = """
    SELECT vcv_id, title, date_last_updated, variation_type
    FROM variants
    WHERE date_last_updated IS NOT NULL
    ORDER BY date_last_updated DESC
    LIMIT 5
    """
    execute_sql_query(db_path, query3, "Variantes actualizadas más recientemente")
    
    # 4. Distribución por ensamblaje genómico
    query4 = """
    SELECT assembly, COUNT(*) as location_count
    FROM genomic_locations
    WHERE assembly IS NOT NULL
    GROUP BY assembly
    ORDER BY location_count DESC
    """
    execute_sql_query(db_path, query4, "Distribución por ensamblaje genómico")
    
    # 5. Variantes con ubicaciones en múltiples cromosomas
    query5 = """
    SELECT v.vcv_id, v.title, COUNT(DISTINCT gl.chromosome) as chromosome_count
    FROM variants v
    JOIN genomic_locations gl ON v.id = gl.variant_id
    WHERE gl.chromosome IS NOT NULL
    GROUP BY v.id
    HAVING chromosome_count > 1
    ORDER BY chromosome_count DESC
    LIMIT 5
    """
    execute_sql_query(db_path, query5, "Variantes en múltiples cromosomas")
    
    # 6. Condiciones más específicas (no genéricas)
    query6 = """
    SELECT condition_name, COUNT(*) as variant_count
    FROM conditions
    WHERE condition_name IS NOT NULL 
    AND condition_name NOT IN ('not provided', 'none provided', 'not specified')
    AND LENGTH(condition_name) > 10
    GROUP BY condition_name
    ORDER BY variant_count DESC
    LIMIT 5
    """
    execute_sql_query(db_path, query6, "Condiciones médicas más específicas")
    
    # 7. Submitters con fechas más recientes
    query7 = """
    SELECT submitter_name, MAX(submission_date) as latest_submission, COUNT(*) as total_submissions
    FROM submitters
    WHERE submitter_name IS NOT NULL AND submission_date IS NOT NULL
    GROUP BY submitter_name
    ORDER BY latest_submission DESC
    LIMIT 5
    """
    execute_sql_query(db_path, query7, "Submitters con envíos más recientes")
    
    # 8. Variantes con rangos genómicos más grandes
    query8 = """
    SELECT v.vcv_id, v.title, gl.chromosome, 
           (gl.stop_position - gl.start_position) as range_size,
           gl.assembly
    FROM variants v
    JOIN genomic_locations gl ON v.id = gl.variant_id
    WHERE gl.start_position IS NOT NULL AND gl.stop_position IS NOT NULL
    AND gl.stop_position > gl.start_position
    ORDER BY range_size DESC
    LIMIT 5
    """
    execute_sql_query(db_path, query8, "Variantes con rangos genómicos más grandes")
    
    # 9. Resumen por tipo de variante y cromosoma
    query9 = """
    SELECT v.variation_type, gl.chromosome, COUNT(*) as count
    FROM variants v
    JOIN genomic_locations gl ON v.id = gl.variant_id
    WHERE v.variation_type IS NOT NULL AND gl.chromosome IS NOT NULL
    GROUP BY v.variation_type, gl.chromosome
    ORDER BY count DESC
    LIMIT 10
    """
    execute_sql_query(db_path, query9, "Tipos de variante por cromosoma (top 10)")
    
    # 10. Estadísticas generales
    query10 = """
    SELECT 
        'Variantes' as tipo, COUNT(*) as total FROM variants
    UNION ALL
    SELECT 'Genes únicos', COUNT(DISTINCT gene_symbol) FROM genes WHERE gene_symbol IS NOT NULL
    UNION ALL
    SELECT 'Condiciones únicas', COUNT(DISTINCT condition_name) FROM conditions WHERE condition_name IS NOT NULL
    UNION ALL
    SELECT 'Submitters únicos', COUNT(DISTINCT submitter_name) FROM submitters WHERE submitter_name IS NOT NULL
    UNION ALL
    SELECT 'Cromosomas únicos', COUNT(DISTINCT chromosome) FROM genomic_locations WHERE chromosome IS NOT NULL
    """
    execute_sql_query(db_path, query10, "Estadísticas generales de la base de datos")

if __name__ == "__main__":
    main()
