import sqlite3 from 'sqlite3';
import { promisify } from 'util';

class Database {
  constructor(dbPath = './clinvar.db') {
    this.db = new sqlite3.Database(dbPath);
    this.run = promisify(this.db.run.bind(this.db));
    this.get = promisify(this.db.get.bind(this.db));
    this.all = promisify(this.db.all.bind(this.db));
  }

  async initialize() {
    console.log('Inicializando base de datos...');
    
    // Tabla principal para variantes
    await this.run(`
      CREATE TABLE IF NOT EXISTS variants (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        vcv_id TEXT UNIQUE NOT NULL,
        title TEXT,
        species TEXT,
        date_created TEXT,
        date_last_updated TEXT,
        review_status TEXT,
        clinical_significance TEXT,
        interpretation_description TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Tabla para genes
    await this.run(`
      CREATE TABLE IF NOT EXISTS genes (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        variant_id INTEGER,
        gene_id TEXT,
        gene_symbol TEXT,
        hgnc_id TEXT,
        FOREIGN KEY (variant_id) REFERENCES variants (id)
      )
    `);

    // Tabla para condiciones
    await this.run(`
      CREATE TABLE IF NOT EXISTS conditions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        variant_id INTEGER,
        condition_name TEXT,
        condition_id TEXT,
        db_name TEXT,
        FOREIGN KEY (variant_id) REFERENCES variants (id)
      )
    `);

    // Tabla para ubicaciones genómicas
    await this.run(`
      CREATE TABLE IF NOT EXISTS genomic_locations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        variant_id INTEGER,
        chromosome TEXT,
        start_position INTEGER,
        stop_position INTEGER,
        reference_allele TEXT,
        alternate_allele TEXT,
        assembly TEXT,
        FOREIGN KEY (variant_id) REFERENCES variants (id)
      )
    `);

    // Tabla para submitters
    await this.run(`
      CREATE TABLE IF NOT EXISTS submitters (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        variant_id INTEGER,
        submitter_name TEXT,
        submission_date TEXT,
        FOREIGN KEY (variant_id) REFERENCES variants (id)
      )
    `);

    console.log('Base de datos inicializada correctamente');
  }

  async insertVariant(variantData) {
    const {
      vcv_id, title, species, date_created, date_last_updated,
      review_status, clinical_significance, interpretation_description
    } = variantData;

    const result = await this.run(`
      INSERT OR REPLACE INTO variants 
      (vcv_id, title, species, date_created, date_last_updated, review_status, clinical_significance, interpretation_description)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `, [vcv_id, title, species, date_created, date_last_updated, review_status, clinical_significance, interpretation_description]);

    return result.lastID;
  }

  async insertGene(variantId, geneData) {
    const { gene_id, gene_symbol, hgnc_id } = geneData;
    await this.run(`
      INSERT INTO genes (variant_id, gene_id, gene_symbol, hgnc_id)
      VALUES (?, ?, ?, ?)
    `, [variantId, gene_id, gene_symbol, hgnc_id]);
  }

  async insertCondition(variantId, conditionData) {
    const { condition_name, condition_id, db_name } = conditionData;
    await this.run(`
      INSERT INTO conditions (variant_id, condition_name, condition_id, db_name)
      VALUES (?, ?, ?, ?)
    `, [variantId, condition_name, condition_id, db_name]);
  }

  async insertGenomicLocation(variantId, locationData) {
    const { chromosome, start_position, stop_position, reference_allele, alternate_allele, assembly } = locationData;
    await this.run(`
      INSERT INTO genomic_locations (variant_id, chromosome, start_position, stop_position, reference_allele, alternate_allele, assembly)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `, [variantId, chromosome, start_position, stop_position, reference_allele, alternate_allele, assembly]);
  }

  async insertSubmitter(variantId, submitterData) {
    const { submitter_name, submission_date } = submitterData;
    await this.run(`
      INSERT INTO submitters (variant_id, submitter_name, submission_date)
      VALUES (?, ?, ?)
    `, [variantId, submitter_name, submission_date]);
  }

  async getVariantCount() {
    const result = await this.get('SELECT COUNT(*) as count FROM variants');
    return result.count;
  }

  async getVariants(limit = 10, offset = 0) {
    return await this.all(`
      SELECT * FROM variants 
      ORDER BY date_last_updated DESC 
      LIMIT ? OFFSET ?
    `, [limit, offset]);
  }

  close() {
    this.db.close();
  }
}

export default Database;
