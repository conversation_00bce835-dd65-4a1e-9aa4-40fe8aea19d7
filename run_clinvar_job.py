#!/usr/bin/env python3
"""
ClinVar Processing Job - Script principal para ejecutar el procesamiento completo
Ejecuta el job completo: lee, parsea el XML y carga los datos en la base de datos
"""

import os
import sys
import time
import argparse
import logging
from datetime import datetime
from database import ClinVarDatabase
from xml_parser import ClinVarXMLParser

class ClinVarJob:
    def __init__(self, xml_file="part_000.xml", db_file="clinvar.db", log_level="INFO"):
        self.xml_file = xml_file
        self.db_file = db_file
        self.db = ClinVarDatabase(db_file)
        self.parser = ClinVarXMLParser()
        
        # Contadores de progreso
        self.processed_count = 0
        self.error_count = 0
        self.start_time = None
        self.total_variants = 0
        
        # Configurar logging
        self.setup_logging(log_level)
        
    def setup_logging(self, log_level):
        """Configurar sistema de logging"""
        log_filename = f"clinvar_job_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        
        logging.basicConfig(
            level=getattr(logging, log_level.upper()),
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_filename),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        self.logger = logging.getLogger(__name__)
        self.logger.info(f"Log iniciado: {log_filename}")
    
    def validate_prerequisites(self):
        """Validar que todos los prerequisitos estén cumplidos"""
        self.logger.info("🔍 Validando prerequisitos...")
        
        # Verificar archivo XML
        if not os.path.exists(self.xml_file):
            self.logger.error(f"❌ Archivo XML no encontrado: {self.xml_file}")
            
            # Buscar archivos XML alternativos
            xml_files = [f for f in os.listdir(".") if f.endswith(".xml")]
            if xml_files:
                self.logger.info("📁 Archivos XML disponibles:")
                for xml_file in xml_files:
                    size = os.path.getsize(xml_file)
                    self.logger.info(f"   - {xml_file} ({size/1024/1024:.1f} MB)")
            
            return False
        
        # Verificar tamaño del archivo
        file_size = os.path.getsize(self.xml_file)
        self.logger.info(f"📁 Archivo XML: {self.xml_file} ({file_size/1024/1024:.1f} MB)")
        
        # Verificar dependencias
        try:
            import xml.etree.ElementTree as ET
            import sqlite3
            self.logger.info("✅ Dependencias verificadas")
        except ImportError as e:
            self.logger.error(f"❌ Dependencia faltante: {e}")
            return False
        
        # Verificar espacio en disco (estimación: XML comprimido ~10x en DB)
        free_space = self.get_free_disk_space()
        estimated_db_size = file_size / 10  # Estimación conservadora
        
        if free_space < estimated_db_size * 2:  # Factor de seguridad 2x
            self.logger.warning(f"⚠️  Espacio en disco limitado: {free_space/1024/1024:.1f} MB disponibles")
            self.logger.warning(f"   Tamaño estimado de DB: {estimated_db_size/1024/1024:.1f} MB")
        
        self.logger.info("✅ Prerequisitos validados correctamente")
        return True
    
    def get_free_disk_space(self):
        """Obtener espacio libre en disco"""
        try:
            import shutil
            return shutil.disk_usage(".").free
        except:
            return float('inf')  # Si no se puede determinar, asumir espacio suficiente
    
    def initialize_database(self):
        """Inicializar la base de datos"""
        self.logger.info("🗄️  Inicializando base de datos...")
        
        try:
            self.db.connect()
            self.db.initialize_database()
            
            # Verificar si ya hay datos
            existing_count = self.db.get_variant_count()
            if existing_count > 0:
                self.logger.warning(f"⚠️  La base de datos ya contiene {existing_count} variantes")
                response = input("¿Deseas continuar y agregar más datos? (y/N): ")
                if response.lower() != 'y':
                    self.logger.info("❌ Procesamiento cancelado por el usuario")
                    return False
            
            self.logger.info("✅ Base de datos inicializada correctamente")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Error inicializando base de datos: {e}")
            return False
    
    def parse_xml_structure(self):
        """Parsear estructura del XML y contar variantes"""
        self.logger.info("📖 Analizando estructura del XML...")
        
        try:
            root = self.parser.parse_file(self.xml_file)
            variation_archives = self.parser.get_variation_archives(root)
            self.total_variants = len(variation_archives)
            
            self.logger.info(f"🔍 Encontradas {self.total_variants:,} variantes en el XML")
            
            if self.total_variants == 0:
                self.logger.error("❌ No se encontraron variantes en el archivo XML")
                return None
            
            return variation_archives
            
        except Exception as e:
            self.logger.error(f"❌ Error parseando XML: {e}")
            return None
    
    def process_variants(self, variation_archives, max_variants=None):
        """Procesar todas las variantes"""
        self.logger.info("🚀 Iniciando procesamiento de variantes...")
        
        # Limitar número de variantes si se especifica
        if max_variants and max_variants < len(variation_archives):
            variation_archives = variation_archives[:max_variants]
            self.total_variants = max_variants
            self.logger.info(f"🎯 Limitando procesamiento a {max_variants:,} variantes")
        
        self.start_time = time.time()
        
        # Procesar cada variante
        for i, variant_archive in enumerate(variation_archives, 1):
            try:
                self.process_single_variant(variant_archive, i)
                
                # Mostrar progreso
                if i % 100 == 0 or i <= 10:
                    self.show_progress(i)
                
                # Checkpoint cada 1000 variantes
                if i % 1000 == 0:
                    self.create_checkpoint(i)
                    
            except KeyboardInterrupt:
                self.logger.warning(f"\n⏹️  Procesamiento interrumpido por el usuario en variante {i}")
                break
            except Exception as e:
                self.logger.error(f"❌ Error crítico en variante {i}: {e}")
                self.error_count += 1
                
                # Si hay demasiados errores consecutivos, parar
                if self.error_count > 50 and i < 100:
                    self.logger.error("❌ Demasiados errores al inicio. Deteniendo procesamiento.")
                    break
        
        self.show_final_statistics()
    
    def process_single_variant(self, variation_archive, current_index):
        """Procesar una variante individual"""
        try:
            # Extraer datos de la variante
            variant_data = self.parser.extract_variant_data(variation_archive)
            if not variant_data or not variant_data.get('vcv_id'):
                if current_index <= 10:  # Solo log para las primeras 10
                    self.logger.warning(f"⚠️  Variante {current_index}: Sin datos válidos")
                self.error_count += 1
                return
            
            vcv_id = variant_data['vcv_id']
            
            # Insertar variante principal
            variant_id = self.db.insert_variant(variant_data)
            
            # Procesar datos relacionados
            self.process_related_data(variant_id, variation_archive)
            
            self.processed_count += 1
            
            # Log detallado para las primeras variantes
            if current_index <= 5:
                self.logger.info(f"✅ Variante {current_index}: {vcv_id} - {variant_data.get('title', 'Sin título')[:50]}...")
            
        except Exception as e:
            if current_index <= 10:
                self.logger.error(f"❌ Error procesando variante {current_index}: {e}")
            self.error_count += 1
    
    def process_related_data(self, variant_id, variation_archive):
        """Procesar datos relacionados de una variante"""
        try:
            # Genes
            genes = self.parser.extract_genes(variation_archive)
            for gene in genes:
                self.db.insert_gene(variant_id, gene)
            
            # Condiciones
            conditions = self.parser.extract_conditions(variation_archive)
            for condition in conditions:
                self.db.insert_condition(variant_id, condition)
            
            # Ubicaciones genómicas
            locations = self.parser.extract_genomic_locations(variation_archive)
            for location in locations:
                self.db.insert_genomic_location(variant_id, location)
            
            # Submitters
            submitters = self.parser.extract_submitters(variation_archive)
            for submitter in submitters:
                self.db.insert_submitter(variant_id, submitter)
                
        except Exception as e:
            self.logger.error(f"❌ Error procesando datos relacionados: {e}")
    
    def show_progress(self, current):
        """Mostrar progreso del procesamiento"""
        elapsed = time.time() - self.start_time
        rate = current / elapsed if elapsed > 0 else 0
        eta = (self.total_variants - current) / rate if rate > 0 else 0
        
        percentage = (current / self.total_variants) * 100
        
        self.logger.info(f"⏳ Progreso: {current:,}/{self.total_variants:,} ({percentage:.1f}%) - "
                        f"Velocidad: {rate:.1f} var/seg - ETA: {eta/60:.1f} min")
    
    def create_checkpoint(self, current):
        """Crear checkpoint del progreso"""
        self.logger.info(f"💾 Checkpoint: {current:,} variantes procesadas")
        self.logger.info(f"   ✅ Exitosas: {self.processed_count:,}")
        self.logger.info(f"   ❌ Errores: {self.error_count:,}")
        
        # Verificar estado de la base de datos
        db_count = self.db.get_variant_count()
        self.logger.info(f"   🗄️  Variantes en DB: {db_count:,}")
    
    def show_final_statistics(self):
        """Mostrar estadísticas finales"""
        elapsed_time = time.time() - self.start_time
        
        self.logger.info(f"\n🎉 PROCESAMIENTO COMPLETADO")
        self.logger.info(f"=" * 40)
        self.logger.info(f"📊 Variantes procesadas exitosamente: {self.processed_count:,}")
        self.logger.info(f"❌ Errores encontrados: {self.error_count:,}")
        self.logger.info(f"⏱️  Tiempo total: {elapsed_time/60:.1f} minutos")
        
        if self.processed_count > 0:
            rate = self.processed_count / elapsed_time
            self.logger.info(f"🚀 Velocidad promedio: {rate:.2f} variantes/segundo")
        
        # Estadísticas de la base de datos
        try:
            final_count = self.db.get_variant_count()
            self.logger.info(f"🗄️  Total en base de datos: {final_count:,} variantes")
            
            if final_count > 0:
                self.logger.info(f"📁 Base de datos guardada como: {self.db_file}")
                db_size = os.path.getsize(self.db_file)
                self.logger.info(f"📏 Tamaño de base de datos: {db_size/1024/1024:.1f} MB")
                
        except Exception as e:
            self.logger.error(f"❌ Error obteniendo estadísticas finales: {e}")
    
    def run(self, max_variants=None):
        """Ejecutar el job completo"""
        self.logger.info("🧬 INICIANDO JOB DE PROCESAMIENTO CLINVAR")
        self.logger.info("=" * 50)
        self.logger.info(f"📁 Archivo XML: {self.xml_file}")
        self.logger.info(f"🗄️  Base de datos: {self.db_file}")
        
        try:
            # 1. Validar prerequisitos
            if not self.validate_prerequisites():
                return False
            
            # 2. Inicializar base de datos
            if not self.initialize_database():
                return False
            
            # 3. Parsear estructura XML
            variation_archives = self.parse_xml_structure()
            if variation_archives is None:
                return False
            
            # 4. Procesar variantes
            self.process_variants(variation_archives, max_variants)
            
            self.logger.info("✅ Job completado exitosamente")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Error crítico en el job: {e}")
            return False
        finally:
            # Cerrar conexiones
            try:
                self.db.close()
                self.logger.info("🔒 Conexiones cerradas")
            except:
                pass

def main():
    """Función principal"""
    parser = argparse.ArgumentParser(description='Job de procesamiento de ClinVar XML')
    parser.add_argument('--xml', '-x', default='part_000.xml',
                       help='Archivo XML de ClinVar (default: part_000.xml)')
    parser.add_argument('--database', '-d', default='clinvar.db',
                       help='Archivo de base de datos (default: clinvar.db)')
    parser.add_argument('--limit', '-l', type=int,
                       help='Número máximo de variantes a procesar')
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       default='INFO', help='Nivel de logging (default: INFO)')
    
    args = parser.parse_args()
    
    # Crear y ejecutar job
    job = ClinVarJob(
        xml_file=args.xml,
        db_file=args.database,
        log_level=args.log_level
    )
    
    success = job.run(max_variants=args.limit)
    
    if success:
        print(f"\n🎉 Job completado exitosamente!")
        print(f"📁 Base de datos: {args.database}")
        print(f"💡 Para consultar datos: python query_clinvar.py --database {args.database}")
        sys.exit(0)
    else:
        print(f"\n❌ Job falló. Revisa los logs para más detalles.")
        sys.exit(1)

if __name__ == "__main__":
    main()
