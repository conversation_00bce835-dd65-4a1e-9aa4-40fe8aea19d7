#!/usr/bin/env python3
"""
Script de diagnóstico para examinar la estructura del XML de ClinVar
"""

import xml.etree.ElementTree as ET
import os

def analyze_xml_structure(file_path, max_depth=3):
    """Analizar la estructura del XML"""
    print(f"🔍 Analizando estructura de: {file_path}")
    
    if not os.path.exists(file_path):
        print(f"❌ Archivo no encontrado: {file_path}")
        return
    
    try:
        # Parsear XML
        tree = ET.parse(file_path)
        root = tree.getroot()
        
        print(f"📋 Elemento raíz: {root.tag}")
        print(f"📋 Atributos del raíz: {root.attrib}")
        print(f"📋 Namespace: {root.tag.split('}')[0] + '}' if '}' in root.tag else 'Sin namespace'}")
        
        # Mostrar estructura jerárquica
        print(f"\n🌳 Estructura del XML (hasta nivel {max_depth}):")
        show_element_structure(root, 0, max_depth)
        
        # Buscar elementos VariationArchive
        print(f"\n🔍 Buscando elementos VariationArchive...")
        variation_archives = root.findall('.//VariationArchive')
        print(f"📊 Encontrados {len(variation_archives)} elementos VariationArchive")
        
        if len(variation_archives) > 0:
            print(f"\n📝 Analizando primer VariationArchive:")
            first_archive = variation_archives[0]
            show_element_structure(first_archive, 0, 2)
            
            # Buscar elementos específicos
            print(f"\n🔍 Buscando elementos específicos en el primer VariationArchive:")
            
            # Buscar Accession
            accessions = first_archive.findall('.//Accession')
            print(f"   - Accession elements: {len(accessions)}")
            for acc in accessions[:3]:  # Mostrar solo los primeros 3
                print(f"     * {acc.tag}: {acc.attrib}")
            
            # Buscar InterpretedRecord
            interpreted_records = first_archive.findall('.//InterpretedRecord')
            print(f"   - InterpretedRecord elements: {len(interpreted_records)}")
            
            # Buscar Gene
            genes = first_archive.findall('.//Gene')
            print(f"   - Gene elements: {len(genes)}")
            for gene in genes[:3]:
                print(f"     * {gene.tag}: {gene.attrib}")
        
        # Mostrar algunos elementos de ejemplo
        print(f"\n📋 Primeros 10 elementos hijos del raíz:")
        for i, child in enumerate(root[:10]):
            print(f"   {i+1}. {child.tag} - Atributos: {child.attrib}")
            
    except ET.ParseError as e:
        print(f"❌ Error parseando XML: {e}")
    except Exception as e:
        print(f"❌ Error inesperado: {e}")

def show_element_structure(element, depth, max_depth):
    """Mostrar estructura jerárquica de un elemento"""
    if depth > max_depth:
        return
    
    indent = "  " * depth
    tag_name = element.tag.split('}')[-1] if '}' in element.tag else element.tag
    
    # Contar hijos únicos
    child_tags = {}
    for child in element:
        child_tag = child.tag.split('}')[-1] if '}' in child.tag else child.tag
        child_tags[child_tag] = child_tags.get(child_tag, 0) + 1
    
    attrs_str = f" (attrs: {len(element.attrib)})" if element.attrib else ""
    text_str = f" [text: '{element.text.strip()[:30]}...']" if element.text and element.text.strip() else ""
    
    print(f"{indent}{tag_name}{attrs_str}{text_str}")
    
    if child_tags and depth < max_depth:
        for child_tag, count in list(child_tags.items())[:5]:  # Mostrar solo los primeros 5 tipos
            print(f"{indent}  └─ {child_tag} ({count})")
            
            # Mostrar estructura del primer hijo de este tipo
            first_child = next((child for child in element if child.tag.endswith(child_tag)), None)
            if first_child and depth + 1 < max_depth:
                show_element_structure(first_child, depth + 2, max_depth)

def find_sample_data(file_path):
    """Buscar datos de muestra en el XML"""
    print(f"\n🔍 Buscando datos de muestra...")
    
    try:
        tree = ET.parse(file_path)
        root = tree.getroot()
        
        # Buscar diferentes tipos de elementos
        elements_to_find = [
            'VariationArchive',
            'Accession',
            'InterpretedRecord',
            'SimpleVariationInterpretation',
            'VariationDescriptor',
            'Gene',
            'ClinicalAssertion'
        ]
        
        for element_name in elements_to_find:
            elements = root.findall(f'.//{element_name}')
            print(f"📊 {element_name}: {len(elements)} encontrados")
            
            if elements:
                first_elem = elements[0]
                print(f"   Primer {element_name}:")
                print(f"     - Atributos: {first_elem.attrib}")
                if first_elem.text and first_elem.text.strip():
                    print(f"     - Texto: {first_elem.text.strip()[:100]}...")
                
                # Mostrar algunos hijos
                children = list(first_elem)[:3]
                if children:
                    print(f"     - Primeros hijos:")
                    for child in children:
                        child_tag = child.tag.split('}')[-1] if '}' in child.tag else child.tag
                        print(f"       * {child_tag}: {child.attrib}")
                print()
                
    except Exception as e:
        print(f"❌ Error buscando datos de muestra: {e}")

def main():
    xml_file = "part_000.xml"
    
    if not os.path.exists(xml_file):
        print(f"❌ Archivo no encontrado: {xml_file}")
        print("📁 Archivos XML disponibles:")
        for file in os.listdir("."):
            if file.endswith(".xml"):
                print(f"   - {file}")
        return
    
    # Obtener información básica del archivo
    file_size = os.path.getsize(xml_file)
    print(f"📁 Archivo: {xml_file}")
    print(f"📏 Tamaño: {file_size:,} bytes ({file_size/1024/1024:.1f} MB)")
    
    # Analizar estructura
    analyze_xml_structure(xml_file)
    
    # Buscar datos de muestra
    find_sample_data(xml_file)

if __name__ == "__main__":
    main()
