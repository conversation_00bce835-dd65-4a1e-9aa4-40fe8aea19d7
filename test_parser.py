#!/usr/bin/env python3
"""
Script de prueba para verificar que el parser funciona correctamente
"""

from xml_parser import ClinVarXMLParser
from database import ClinVarDatabase
import os

def test_parser():
    """Probar el parser con las primeras variantes"""
    print("🧪 Probando el parser de ClinVar...")
    
    xml_file = "part_000.xml"
    if not os.path.exists(xml_file):
        print(f"❌ Archivo no encontrado: {xml_file}")
        return
    
    parser = ClinVarXMLParser()
    
    try:
        # Parsear XML
        root = parser.parse_file(xml_file)
        
        # Obtener las primeras 5 variantes
        variation_archives = parser.get_variation_archives(root)
        print(f"📊 Total de variantes en el archivo: {len(variation_archives)}")
        
        print(f"\n🔍 Probando con las primeras 5 variantes:")
        
        for i, variant_archive in enumerate(variation_archives[:5], 1):
            print(f"\n--- Variante {i} ---")
            
            # Extraer datos de la variante
            variant_data = parser.extract_variant_data(variant_archive)
            if variant_data:
                print(f"✅ Datos de variante extraídos:")
                for key, value in variant_data.items():
                    print(f"   {key}: {value}")
                
                # Extraer genes
                genes = parser.extract_genes(variant_archive)
                print(f"🧬 Genes encontrados: {len(genes)}")
                for gene in genes[:2]:  # Mostrar solo los primeros 2
                    print(f"   - {gene}")
                
                # Extraer condiciones
                conditions = parser.extract_conditions(variant_archive)
                print(f"🏥 Condiciones encontradas: {len(conditions)}")
                for condition in conditions[:2]:
                    print(f"   - {condition}")
                
                # Extraer ubicaciones genómicas
                locations = parser.extract_genomic_locations(variant_archive)
                print(f"📍 Ubicaciones genómicas: {len(locations)}")
                for location in locations[:2]:
                    print(f"   - {location}")
                
                # Extraer submitters
                submitters = parser.extract_submitters(variant_archive)
                print(f"👥 Submitters: {len(submitters)}")
                for submitter in submitters[:2]:
                    print(f"   - {submitter}")
            else:
                print(f"❌ No se pudieron extraer datos de la variante {i}")
                
    except Exception as e:
        print(f"❌ Error en la prueba: {e}")

def test_database_insertion():
    """Probar inserción en base de datos"""
    print(f"\n🗄️  Probando inserción en base de datos...")
    
    db = ClinVarDatabase("test_clinvar.db")
    
    try:
        db.connect()
        db.initialize_database()
        
        # Datos de prueba
        test_variant = {
            'vcv_id': 'VCV000000001',
            'title': 'Test variant',
            'species': 'Homo sapiens',
            'variation_type': 'single nucleotide variant',
            'date_created': '2023-01-01',
            'date_last_updated': '2023-01-02',
            'review_status': 'reviewed by expert panel',
            'clinical_significance': 'Pathogenic',
            'interpretation_description': 'Test description'
        }
        
        # Insertar variante
        variant_id = db.insert_variant(test_variant)
        print(f"✅ Variante insertada con ID: {variant_id}")
        
        # Insertar gene de prueba
        test_gene = {
            'gene_id': '1234',
            'gene_symbol': 'TEST1',
            'hgnc_id': 'HGNC:1234',
            'full_name': 'Test gene 1'
        }
        
        db.insert_gene(variant_id, test_gene)
        print(f"✅ Gen insertado")
        
        # Verificar datos
        count = db.get_variant_count()
        print(f"📊 Total de variantes en la base de datos: {count}")
        
        variants = db.get_variants(1)
        if variants:
            variant = variants[0]
            print(f"✅ Variante recuperada: {variant['vcv_id']} - {variant['title']}")
        
    except Exception as e:
        print(f"❌ Error en prueba de base de datos: {e}")
    finally:
        db.close()
        # Limpiar archivo de prueba
        if os.path.exists("test_clinvar.db"):
            os.remove("test_clinvar.db")
            print("🧹 Archivo de prueba eliminado")

def main():
    test_parser()
    test_database_insertion()
    print(f"\n✅ Pruebas completadas")

if __name__ == "__main__":
    main()
