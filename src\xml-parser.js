import xml2js from 'xml2js';
import fs from 'fs-extra';

class ClinVarXMLParser {
  constructor() {
    this.parser = new xml2js.Parser({
      explicitArray: false,
      mergeAttrs: true,
      normalize: true,
      normalizeTags: true,
      trim: true
    });
  }

  async parseFile(filePath) {
    console.log(`Leyendo archivo XML: ${filePath}`);
    const xmlData = await fs.readFile(filePath, 'utf8');
    
    console.log('Parseando XML...');
    const result = await this.parser.parseStringPromise(xmlData);
    
    return result;
  }

  extractVariantData(variationArchive) {
    try {
      const vcvAccession = variationArchive.accession;
      const vcvId = vcvAccession ? vcvAccession.acc : null;
      
      const interpretedRecord = variationArchive.interpretedrecord;
      if (!interpretedRecord) return null;

      // Información básica
      const title = interpretedRecord.simplevariationinterpretation?.variationdescriptor?.name || 'Sin título';
      const species = interpretedRecord.species || 'Homo sapiens';
      
      // Fechas
      const dateCreated = vcvAccession?.datecreated || null;
      const dateLastUpdated = vcvAccession?.datelastupdated || null;

      // Significancia clínica
      const clinicalAssertions = interpretedRecord.clinicalassertionlist?.clinicalassertion;
      let reviewStatus = null;
      let clinicalSignificance = null;
      let interpretationDescription = null;

      if (clinicalAssertions) {
        const assertions = Array.isArray(clinicalAssertions) ? clinicalAssertions : [clinicalAssertions];
        const firstAssertion = assertions[0];
        
        if (firstAssertion) {
          reviewStatus = firstAssertion.reviewstatus || null;
          
          const interpretation = firstAssertion.interpretation;
          if (interpretation) {
            clinicalSignificance = interpretation.description || null;
            interpretationDescription = interpretation.comment || null;
          }
        }
      }

      return {
        vcv_id: vcvId,
        title,
        species,
        date_created: dateCreated,
        date_last_updated: dateLastUpdated,
        review_status: reviewStatus,
        clinical_significance: clinicalSignificance,
        interpretation_description: interpretationDescription
      };
    } catch (error) {
      console.error('Error extrayendo datos de variante:', error);
      return null;
    }
  }

  extractGenes(variationArchive) {
    const genes = [];
    try {
      const interpretedRecord = variationArchive.interpretedrecord;
      const variationDescriptor = interpretedRecord?.simplevariationinterpretation?.variationdescriptor;
      
      if (variationDescriptor?.gene) {
        const geneList = Array.isArray(variationDescriptor.gene) ? variationDescriptor.gene : [variationDescriptor.gene];
        
        geneList.forEach(gene => {
          genes.push({
            gene_id: gene.id || null,
            gene_symbol: gene.symbol || null,
            hgnc_id: gene.hgncid || null
          });
        });
      }
    } catch (error) {
      console.error('Error extrayendo genes:', error);
    }
    return genes;
  }

  extractConditions(variationArchive) {
    const conditions = [];
    try {
      const interpretedRecord = variationArchive.interpretedrecord;
      const clinicalAssertions = interpretedRecord?.clinicalassertionlist?.clinicalassertion;
      
      if (clinicalAssertions) {
        const assertions = Array.isArray(clinicalAssertions) ? clinicalAssertions : [clinicalAssertions];
        
        assertions.forEach(assertion => {
          const traitSet = assertion.traitset;
          if (traitSet?.trait) {
            const traits = Array.isArray(traitSet.trait) ? traitSet.trait : [traitSet.trait];
            
            traits.forEach(trait => {
              if (trait.name) {
                const names = Array.isArray(trait.name) ? trait.name : [trait.name];
                names.forEach(name => {
                  conditions.push({
                    condition_name: name.elementvalue || null,
                    condition_id: trait.id || null,
                    db_name: name.type || null
                  });
                });
              }
            });
          }
        });
      }
    } catch (error) {
      console.error('Error extrayendo condiciones:', error);
    }
    return conditions;
  }

  extractGenomicLocations(variationArchive) {
    const locations = [];
    try {
      const interpretedRecord = variationArchive.interpretedrecord;
      const variationDescriptor = interpretedRecord?.simplevariationinterpretation?.variationdescriptor;
      
      if (variationDescriptor?.location) {
        const locationList = Array.isArray(variationDescriptor.location) ? variationDescriptor.location : [variationDescriptor.location];
        
        locationList.forEach(location => {
          const sequenceLocation = location.sequencelocation;
          if (sequenceLocation) {
            locations.push({
              chromosome: sequenceLocation.chr || null,
              start_position: sequenceLocation.start ? parseInt(sequenceLocation.start) : null,
              stop_position: sequenceLocation.stop ? parseInt(sequenceLocation.stop) : null,
              reference_allele: sequenceLocation.referenceallele || null,
              alternate_allele: sequenceLocation.alternateallele || null,
              assembly: sequenceLocation.assembly || null
            });
          }
        });
      }
    } catch (error) {
      console.error('Error extrayendo ubicaciones genómicas:', error);
    }
    return locations;
  }

  extractSubmitters(variationArchive) {
    const submitters = [];
    try {
      const interpretedRecord = variationArchive.interpretedrecord;
      const clinicalAssertions = interpretedRecord?.clinicalassertionlist?.clinicalassertion;
      
      if (clinicalAssertions) {
        const assertions = Array.isArray(clinicalAssertions) ? clinicalAssertions : [clinicalAssertions];
        
        assertions.forEach(assertion => {
          const submitter = assertion.clinvarsubmissionid;
          if (submitter) {
            submitters.push({
              submitter_name: submitter.submitter || null,
              submission_date: assertion.datesubmitted || null
            });
          }
        });
      }
    } catch (error) {
      console.error('Error extrayendo submitters:', error);
    }
    return submitters;
  }
}

export default ClinVarXMLParser;
