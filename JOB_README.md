# ClinVar Processing Job - Guía de Ejecución

Este documento describe cómo ejecutar el job de procesamiento de ClinVar que lee, parsea el XML y carga los datos en la base de datos.

## 🚀 Ejecución Rápida

### Co<PERSON>o Principal
```bash
python execute_job.py
```

Este comando:
1. **Busca automáticamente** el archivo XML de ClinVar en el directorio
2. **Parsea** toda la estructura XML
3. **Carga** todos los datos en la base de datos `clinvar.db`

## 📋 Opciones de Ejecución

### Procesar número limitado de variantes
```bash
python execute_job.py --limit 1000
```

### Especificar archivo XML específico
```bash
python execute_job.py --xml mi_archivo_clinvar.xml
```

### Especificar base de datos de salida
```bash
python execute_job.py --database mi_base_datos.db
```

### Ejecución silenciosa
```bash
python execute_job.py --quiet
```

### Combinando opciones
```bash
python execute_job.py --xml part_000.xml --database clinvar_2024.db --limit 5000
```

## 🔧 Scripts Disponibles

### 1. `execute_job.py` - **RECOMENDADO**
- **Uso**: Script principal y más completo
- **Características**: 
  - Detección automática de archivos XML
  - Logging detallado con timestamps
  - Estadísticas completas
  - Manejo robusto de errores
  - Estimación de tiempo restante

```bash
python execute_job.py --help
```

### 2. `run_job.py` - Alternativa simplificada
- **Uso**: Versión sin emojis para compatibilidad
- **Características**: Logging simple, sin problemas de encoding

```bash
python run_job.py --limit 100 --database test.db
```

### 3. `quick_job.py` - Ejecución interactiva
- **Uso**: Para usuarios que prefieren menús interactivos
- **Características**: Selección automática de opciones según tamaño del archivo

```bash
python quick_job.py
```

## 📊 Ejemplos de Uso Típicos

### Para desarrollo y pruebas
```bash
# Procesar muestra pequeña
python execute_job.py --limit 100 --database test.db

# Verificar resultados
python query_clinvar.py --database test.db --summary
```

### Para procesamiento de producción
```bash
# Procesar archivo completo
python execute_job.py --database clinvar_production.db

# Procesar con límite para control de tiempo
python execute_job.py --limit 10000 --database clinvar_partial.db
```

### Para análisis específicos
```bash
# Procesar solo una muestra representativa
python execute_job.py --limit 5000 --database clinvar_sample.db

# Consultar genes específicos
python query_clinvar.py --database clinvar_sample.db --gene BRCA1
```

## ⏱️ Tiempos de Procesamiento Estimados

| Variantes | Tiempo Estimado | Tamaño DB | Uso Recomendado |
|-----------|----------------|-----------|-----------------|
| 100       | 30 segundos    | ~50 KB    | Pruebas rápidas |
| 1,000     | 5 minutos      | ~500 KB   | Desarrollo |
| 10,000    | 50 minutos     | ~5 MB     | Análisis parcial |
| 50,000    | 4-5 horas      | ~25 MB    | Archivo completo |

*Tiempos aproximados en hardware típico*

## 📁 Archivos Generados

### Base de datos SQLite
- **Ubicación**: Directorio actual
- **Nombre**: `clinvar.db` (por defecto)
- **Estructura**: 5 tablas relacionales
- **Tamaño**: ~0.5 KB por variante procesada

### Logs (solo con `run_clinvar_job.py`)
- **Formato**: `clinvar_job_YYYYMMDD_HHMMSS.log`
- **Contenido**: Log detallado con timestamps
- **Ubicación**: Directorio actual

## 🔍 Verificación de Resultados

### Verificar que el job funcionó
```bash
python query_clinvar.py --database clinvar.db --summary
```

### Ver variantes procesadas
```bash
python query_clinvar.py --database clinvar.db --gene VWF
```

### Explorar base de datos completa
```bash
python explore_database.py
```

## ⚠️ Consideraciones Importantes

### Requisitos del Sistema
- **Python 3.7+**
- **Espacio en disco**: ~10% del tamaño del XML
- **RAM**: Mínimo 2GB recomendado
- **Tiempo**: Planificar varias horas para archivo completo

### Archivos XML Soportados
- `part_000.xml` (detectado automáticamente)
- Cualquier archivo XML de ClinVar
- Archivos de hasta varios GB

### Interrupción del Procesamiento
- **Ctrl+C**: Detiene el procesamiento de forma segura
- **Datos**: Se preservan las variantes ya procesadas
- **Reinicio**: Se puede continuar desde donde se quedó

## 🐛 Solución de Problemas

### Error: "Archivo XML no encontrado"
```bash
# Verificar archivos disponibles
ls *.xml

# Especificar archivo manualmente
python execute_job.py --xml tu_archivo.xml
```

### Error: "Sin espacio en disco"
```bash
# Procesar menos variantes
python execute_job.py --limit 1000

# Usar base de datos en otra ubicación
python execute_job.py --database /ruta/con/espacio/clinvar.db
```

### Procesamiento muy lento
```bash
# Verificar con muestra pequeña primero
python execute_job.py --limit 100

# Usar SSD si está disponible
# Cerrar otras aplicaciones pesadas
```

### Errores de encoding (Windows)
```bash
# Usar versión sin emojis
python run_job.py --limit 1000
```

## 📈 Monitoreo del Progreso

### Durante la ejecución verás:
```
[13:45:30] Progreso: 1,000/50,000 (2.0%) - Velocidad: 15.2 var/seg - ETA: 53.6 min
[13:46:30] Progreso: 2,000/50,000 (4.0%) - Velocidad: 16.1 var/seg - ETA: 49.2 min
```

### Información mostrada:
- **Progreso**: Variantes procesadas vs total
- **Porcentaje**: Progreso completado
- **Velocidad**: Variantes por segundo
- **ETA**: Tiempo estimado restante

## 🎯 Casos de Uso Recomendados

### 1. Primera vez usando el sistema
```bash
python execute_job.py --limit 100 --database primer_test.db
python query_clinvar.py --database primer_test.db --summary
```

### 2. Análisis de investigación
```bash
python execute_job.py --limit 5000 --database investigacion.db
python query_clinvar.py --database investigacion.db --gene BRCA1
```

### 3. Procesamiento completo para producción
```bash
python execute_job.py --database clinvar_completo.db
```

### 4. Actualización incremental
```bash
python execute_job.py --xml nuevo_archivo.xml --database clinvar_actualizado.db
```

## ✅ Verificación Final

Después de ejecutar el job, verifica que todo funcionó correctamente:

```bash
# 1. Verificar estadísticas
python query_clinvar.py --database clinvar.db --summary

# 2. Buscar una variante específica
python query_clinvar.py --database clinvar.db --variant VCV000000003

# 3. Explorar por gen
python query_clinvar.py --database clinvar.db --gene NF1

# 4. Ver estado del proyecto
python project_status.py
```

¡El job está listo para procesar cualquier archivo XML de ClinVar! 🚀
