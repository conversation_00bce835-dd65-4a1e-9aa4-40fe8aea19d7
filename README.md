# ClinVar XML Processor

Un procesador completo para archivos XML de ClinVar que extrae y almacena datos de variantes genéticas en una base de datos SQLite.

## 🚀 Características

- **Procesamiento completo de XML**: Parsea archivos XML de ClinVar y extrae información detallada
- **Base de datos estructurada**: Almacena datos en SQLite con tablas relacionales
- **Consultas flexibles**: Herramientas para buscar y explorar los datos procesados
- **Procesamiento escalable**: Puede procesar desde muestras pequeñas hasta archivos completos
- **Informes detallados**: Estadísticas y progreso en tiempo real

## 📋 Requisitos

- Python 3.7+
- Dependencias: `lxml` (se instala automáticamente)

## 🛠️ Instalación

1. Clona o descarga los archivos del proyecto
2. Instala las dependencias:
```bash
pip install lxml
```

## 📁 Estructura del proyecto

```
├── clinvar_main.py          # Aplicación principal
├── process_sample.py        # Procesador de muestra (100 variantes)
├── query_clinvar.py         # Herramienta de consulta
├── database.py              # Manejo de base de datos
├── xml_parser.py            # Parser de XML
├── test_parser.py           # Script de pruebas
├── debug_xml.py             # Herramienta de diagnóstico
└── README.md                # Este archivo
```

## 🎯 Uso rápido

### 1. Procesar una muestra pequeña (recomendado para empezar)

```bash
python process_sample.py
```

Esto procesará las primeras 100 variantes y creará `clinvar_sample.db`.

### 2. Ver estadísticas de los datos procesados

```bash
python query_clinvar.py --summary
```

### 3. Buscar variantes por gen

```bash
python query_clinvar.py --gene VWF
```

### 4. Ver detalles de una variante específica

```bash
python query_clinvar.py --variant VCV000000003
```

## 📊 Uso avanzado

### Procesar archivo completo

```bash
python clinvar_main.py --file part_000.xml
```

### Procesar número limitado de variantes

```bash
python clinvar_main.py --limit 1000 --database mi_clinvar.db
```

### Opciones del procesador principal

```bash
python clinvar_main.py --help
```

Opciones disponibles:
- `--file, -f`: Archivo XML a procesar (default: part_000.xml)
- `--limit, -l`: Número máximo de variantes a procesar
- `--database, -d`: Archivo de base de datos (default: clinvar.db)

### Opciones de consulta

```bash
python query_clinvar.py --help
```

Opciones disponibles:
- `--summary, -s`: Mostrar resumen de la base de datos
- `--gene, -g`: Buscar variantes por símbolo de gen
- `--condition, -c`: Buscar variantes por condición
- `--variant, -v`: Mostrar detalles de una variante específica
- `--database, -d`: Archivo de base de datos a consultar

## 🗄️ Estructura de la base de datos

### Tabla `variants`
- `vcv_id`: Identificador único de ClinVar
- `title`: Descripción de la variante
- `species`: Especie (generalmente Homo sapiens)
- `variation_type`: Tipo de variante (SNV, deletion, etc.)
- `date_created`: Fecha de creación
- `date_last_updated`: Fecha de última actualización
- `review_status`: Estado de revisión
- `clinical_significance`: Significancia clínica
- `interpretation_description`: Descripción de la interpretación

### Tabla `genes`
- `gene_id`: ID del gen
- `gene_symbol`: Símbolo del gen
- `hgnc_id`: ID de HGNC
- `full_name`: Nombre completo del gen

### Tabla `conditions`
- `condition_name`: Nombre de la condición
- `condition_id`: ID de la condición
- `db_name`: Base de datos fuente

### Tabla `genomic_locations`
- `chromosome`: Cromosoma
- `start_position`: Posición de inicio
- `stop_position`: Posición de fin
- `reference_allele`: Alelo de referencia
- `alternate_allele`: Alelo alternativo
- `assembly`: Ensamblaje genómico

### Tabla `submitters`
- `submitter_name`: Nombre del submitter
- `submission_date`: Fecha de envío

## 📈 Ejemplos de consultas

### Buscar variantes en un gen específico
```bash
python query_clinvar.py --gene BRCA1
```

### Buscar variantes relacionadas con cáncer
```bash
python query_clinvar.py --condition cancer
```

### Ver estadísticas generales
```bash
python query_clinvar.py --summary
```

## 🔧 Herramientas de diagnóstico

### Analizar estructura del XML
```bash
python debug_xml.py
```

### Probar el parser
```bash
python test_parser.py
```

## 📊 Rendimiento

- **Velocidad**: ~4-5 variantes/segundo en hardware típico
- **Memoria**: Uso eficiente de memoria con procesamiento streaming
- **Escalabilidad**: Puede procesar archivos de varios GB

## 🎯 Casos de uso

1. **Investigación genética**: Buscar variantes en genes de interés
2. **Análisis de condiciones**: Explorar variantes asociadas con enfermedades
3. **Estudios poblacionales**: Analizar distribución de variantes
4. **Validación clínica**: Verificar información de variantes específicas

## ⚠️ Notas importantes

- El archivo XML de ClinVar puede ser muy grande (>800MB)
- Se recomienda empezar con muestras pequeñas para familiarizarse
- La base de datos SQLite resultante será significativamente más pequeña que el XML original
- El procesamiento completo puede tomar varias horas dependiendo del hardware

## 🐛 Solución de problemas

### Error: "Archivo XML no encontrado"
Asegúrate de que el archivo `part_000.xml` esté en el directorio actual.

### Error: "Base de datos no encontrada"
Ejecuta primero el procesador para crear la base de datos.

### Procesamiento lento
- Usa `--limit` para procesar menos variantes
- Verifica que tienes suficiente espacio en disco
- Considera usar un SSD para mejor rendimiento

## 📝 Licencia

Este proyecto es de código abierto y está disponible bajo la licencia MIT.

## 🤝 Contribuciones

Las contribuciones son bienvenidas. Por favor:
1. Haz fork del proyecto
2. Crea una rama para tu feature
3. Commit tus cambios
4. Push a la rama
5. Abre un Pull Request

## 📞 Soporte

Si encuentras problemas o tienes preguntas, por favor abre un issue en el repositorio del proyecto.
