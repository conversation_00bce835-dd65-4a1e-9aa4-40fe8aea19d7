#!/usr/bin/env python3
"""
Script de consulta para explorar los datos de ClinVar procesados
"""

import argparse
import os
from database import ClinVarDatabase

class ClinVarQuery:
    def __init__(self, db_path: str):
        self.db = ClinVarDatabase(db_path)
        
    def connect(self):
        """Conectar a la base de datos"""
        if not os.path.exists(self.db.db_path):
            print(f"❌ Base de datos no encontrada: {self.db.db_path}")
            return False
        
        self.db.connect()
        return True
    
    def show_summary(self):
        """Mostrar resumen de la base de datos"""
        print("📊 Resumen de la base de datos ClinVar")
        print("=" * 40)
        
        try:
            # Estadísticas básicas
            variant_count = self.db.get_variant_count()
            print(f"🧬 Total de variantes: {variant_count:,}")
            
            if variant_count == 0:
                print("⚠️  No hay variantes en la base de datos")
                return
            
            cursor = self.db.connection.cursor()
            
            # Contar genes
            cursor.execute("SELECT COUNT(*) FROM genes")
            gene_count = cursor.fetchone()[0]
            print(f"🧬 Total de genes: {gene_count:,}")
            
            # Contar condiciones
            cursor.execute("SELECT COUNT(*) FROM conditions")
            condition_count = cursor.fetchone()[0]
            print(f"🏥 Total de condiciones: {condition_count:,}")
            
            # Contar ubicaciones genómicas
            cursor.execute("SELECT COUNT(*) FROM genomic_locations")
            location_count = cursor.fetchone()[0]
            print(f"📍 Total de ubicaciones genómicas: {location_count:,}")
            
            # Contar submitters
            cursor.execute("SELECT COUNT(*) FROM submitters")
            submitter_count = cursor.fetchone()[0]
            print(f"👥 Total de submitters: {submitter_count:,}")
            
            # Tipos de variantes más comunes
            print(f"\n🔬 Tipos de variantes más comunes:")
            cursor.execute("""
                SELECT variation_type, COUNT(*) as count 
                FROM variants 
                WHERE variation_type IS NOT NULL 
                GROUP BY variation_type 
                ORDER BY count DESC 
                LIMIT 10
            """)
            
            for row in cursor.fetchall():
                print(f"   - {row[0]}: {row[1]:,}")
            
            # Genes más frecuentes
            print(f"\n🧬 Genes más frecuentes:")
            cursor.execute("""
                SELECT gene_symbol, COUNT(*) as count 
                FROM genes 
                WHERE gene_symbol IS NOT NULL 
                GROUP BY gene_symbol 
                ORDER BY count DESC 
                LIMIT 10
            """)
            
            for row in cursor.fetchall():
                print(f"   - {row[0]}: {row[1]:,} variantes")
                
        except Exception as e:
            print(f"❌ Error mostrando resumen: {e}")
    
    def search_by_gene(self, gene_symbol: str):
        """Buscar variantes por símbolo de gen"""
        print(f"🔍 Buscando variantes del gen: {gene_symbol}")
        print("-" * 40)
        
        try:
            cursor = self.db.connection.cursor()
            cursor.execute("""
                SELECT DISTINCT v.vcv_id, v.title, v.variation_type, v.clinical_significance
                FROM variants v
                JOIN genes g ON v.id = g.variant_id
                WHERE g.gene_symbol = ?
                ORDER BY v.date_last_updated DESC
                LIMIT 20
            """, (gene_symbol,))
            
            results = cursor.fetchall()
            
            if not results:
                print(f"❌ No se encontraron variantes para el gen {gene_symbol}")
                return
            
            print(f"✅ Encontradas {len(results)} variantes para {gene_symbol}:")
            for i, row in enumerate(results, 1):
                vcv_id, title, var_type, significance = row
                print(f"{i}. {vcv_id}")
                print(f"   📝 {title[:70]}...")
                print(f"   🔬 Tipo: {var_type or 'No especificado'}")
                print(f"   🏥 Significancia: {significance or 'No especificada'}")
                print()
                
        except Exception as e:
            print(f"❌ Error buscando por gen: {e}")
    
    def search_by_condition(self, condition_name: str):
        """Buscar variantes por condición"""
        print(f"🔍 Buscando variantes relacionadas con: {condition_name}")
        print("-" * 50)
        
        try:
            cursor = self.db.connection.cursor()
            cursor.execute("""
                SELECT DISTINCT v.vcv_id, v.title, v.variation_type, c.condition_name
                FROM variants v
                JOIN conditions c ON v.id = c.variant_id
                WHERE c.condition_name LIKE ?
                ORDER BY v.date_last_updated DESC
                LIMIT 20
            """, (f"%{condition_name}%",))
            
            results = cursor.fetchall()
            
            if not results:
                print(f"❌ No se encontraron variantes para la condición '{condition_name}'")
                return
            
            print(f"✅ Encontradas {len(results)} variantes relacionadas:")
            for i, row in enumerate(results, 1):
                vcv_id, title, var_type, condition = row
                print(f"{i}. {vcv_id}")
                print(f"   📝 {title[:70]}...")
                print(f"   🔬 Tipo: {var_type or 'No especificado'}")
                print(f"   🏥 Condición: {condition}")
                print()
                
        except Exception as e:
            print(f"❌ Error buscando por condición: {e}")
    
    def show_variant_details(self, vcv_id: str):
        """Mostrar detalles completos de una variante"""
        print(f"🔍 Detalles de la variante: {vcv_id}")
        print("=" * 50)
        
        try:
            variant = self.db.get_variant_details(vcv_id)
            
            if not variant:
                print(f"❌ Variante {vcv_id} no encontrada")
                return
            
            # Información básica
            print(f"📝 Título: {variant['title']}")
            print(f"🔬 Tipo: {variant['variation_type'] or 'No especificado'}")
            print(f"🧬 Especie: {variant['species']}")
            print(f"🏥 Significancia clínica: {variant['clinical_significance'] or 'No especificada'}")
            print(f"📊 Estado de revisión: {variant['review_status'] or 'No especificado'}")
            print(f"📅 Creada: {variant['date_created']}")
            print(f"📅 Última actualización: {variant['date_last_updated']}")
            
            if variant['interpretation_description']:
                print(f"📄 Descripción: {variant['interpretation_description']}")
            
            # Genes
            if variant['genes']:
                print(f"\n🧬 Genes asociados ({len(variant['genes'])}):")
                for gene in variant['genes']:
                    print(f"   - {gene['gene_symbol']} (ID: {gene['gene_id']})")
                    if gene['full_name']:
                        print(f"     {gene['full_name']}")
                    if gene['hgnc_id']:
                        print(f"     HGNC: {gene['hgnc_id']}")
            
            # Condiciones
            if variant['conditions']:
                print(f"\n🏥 Condiciones asociadas ({len(variant['conditions'])}):")
                for condition in variant['conditions'][:10]:  # Mostrar solo las primeras 10
                    print(f"   - {condition['condition_name']}")
                    if condition['db_name']:
                        print(f"     Fuente: {condition['db_name']}")
                if len(variant['conditions']) > 10:
                    print(f"   ... y {len(variant['conditions']) - 10} más")
            
            # Ubicaciones genómicas
            if variant['genomic_locations']:
                print(f"\n📍 Ubicaciones genómicas ({len(variant['genomic_locations'])}):")
                for location in variant['genomic_locations']:
                    if location['chromosome']:
                        pos_str = ""
                        if location['start_position']:
                            pos_str = f":{location['start_position']}"
                            if location['stop_position'] and location['stop_position'] != location['start_position']:
                                pos_str += f"-{location['stop_position']}"
                        
                        print(f"   - Chr{location['chromosome']}{pos_str}")
                        if location['assembly']:
                            print(f"     Ensamblaje: {location['assembly']}")
                        if location['reference_allele'] or location['alternate_allele']:
                            print(f"     Alelos: {location['reference_allele']} → {location['alternate_allele']}")
            
            # Submitters
            if variant['submitters']:
                print(f"\n👥 Submitters ({len(variant['submitters'])}):")
                for submitter in variant['submitters'][:5]:  # Mostrar solo los primeros 5
                    print(f"   - {submitter['submitter_name']}")
                    if submitter['submission_date']:
                        print(f"     Fecha: {submitter['submission_date']}")
                if len(variant['submitters']) > 5:
                    print(f"   ... y {len(variant['submitters']) - 5} más")
                    
        except Exception as e:
            print(f"❌ Error mostrando detalles de variante: {e}")
    
    def close(self):
        """Cerrar conexión"""
        self.db.close()

def main():
    parser = argparse.ArgumentParser(description='Consultar base de datos de ClinVar')
    parser.add_argument('--database', '-d', default='clinvar_sample.db',
                       help='Archivo de base de datos (default: clinvar_sample.db)')
    parser.add_argument('--summary', '-s', action='store_true',
                       help='Mostrar resumen de la base de datos')
    parser.add_argument('--gene', '-g', type=str,
                       help='Buscar variantes por símbolo de gen')
    parser.add_argument('--condition', '-c', type=str,
                       help='Buscar variantes por condición')
    parser.add_argument('--variant', '-v', type=str,
                       help='Mostrar detalles de una variante específica (VCV ID)')
    
    args = parser.parse_args()
    
    # Si no se especifica ninguna acción, mostrar resumen
    if not any([args.summary, args.gene, args.condition, args.variant]):
        args.summary = True
    
    query = ClinVarQuery(args.database)
    
    try:
        if not query.connect():
            return
        
        if args.summary:
            query.show_summary()
        
        if args.gene:
            query.search_by_gene(args.gene)
        
        if args.condition:
            query.search_by_condition(args.condition)
        
        if args.variant:
            query.show_variant_details(args.variant)
            
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        query.close()

if __name__ == "__main__":
    main()
