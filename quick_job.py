#!/usr/bin/env python3
"""
Quick ClinVar Job - Versión simplificada para ejecución rápida
Ejecuta el procesamiento con configuración automática y mínima intervención
"""

import os
import sys
import time
from datetime import datetime
from database import ClinVarDatabase
from xml_parser import ClinVarXMLParser

def find_xml_file():
    """Buscar automáticamente el archivo XML de ClinVar"""
    # Buscar archivos XML en el directorio actual
    xml_files = [f for f in os.listdir(".") if f.endswith(".xml")]
    
    if not xml_files:
        return None
    
    # Priorizar archivos con nombres típicos de ClinVar
    priority_names = ['part_000.xml', 'clinvar.xml', 'ClinVar.xml']
    for name in priority_names:
        if name in xml_files:
            return name
    
    # Si no hay nombres prioritarios, tomar el más grande (probablemente el principal)
    xml_files_with_size = [(f, os.path.getsize(f)) for f in xml_files]
    largest_file = max(xml_files_with_size, key=lambda x: x[1])
    
    return largest_file[0]

def quick_process(xml_file, max_variants=None):
    """Procesamiento rápido y directo"""
    print("🧬 ClinVar Quick Job - Procesamiento Rápido")
    print("=" * 50)
    
    start_time = time.time()
    
    # Configuración automática
    db_file = f"clinvar_quick_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
    
    print(f"📁 XML: {xml_file}")
    print(f"🗄️  DB: {db_file}")
    
    if max_variants:
        print(f"🎯 Límite: {max_variants:,} variantes")
    
    # Inicializar componentes
    db = ClinVarDatabase(db_file)
    parser = ClinVarXMLParser()
    
    processed_count = 0
    error_count = 0
    
    try:
        # Conectar DB
        print("\n🔧 Inicializando base de datos...")
        db.connect()
        db.initialize_database()
        
        # Parsear XML
        print("📖 Parseando XML...")
        root = parser.parse_file(xml_file)
        variation_archives = parser.get_variation_archives(root)
        total_variants = len(variation_archives)
        
        print(f"🔍 Encontradas {total_variants:,} variantes")
        
        if total_variants == 0:
            print("❌ No hay variantes para procesar")
            return False
        
        # Limitar si es necesario
        if max_variants and max_variants < total_variants:
            variation_archives = variation_archives[:max_variants]
            total_variants = max_variants
            print(f"🎯 Procesando {max_variants:,} variantes")
        
        # Procesar variantes
        print(f"\n🚀 Procesando variantes...")
        
        for i, variant_archive in enumerate(variation_archives, 1):
            try:
                # Extraer datos
                variant_data = parser.extract_variant_data(variant_archive)
                if not variant_data or not variant_data.get('vcv_id'):
                    error_count += 1
                    continue
                
                # Insertar variante
                variant_id = db.insert_variant(variant_data)
                
                # Insertar datos relacionados
                for gene in parser.extract_genes(variant_archive):
                    db.insert_gene(variant_id, gene)
                
                for condition in parser.extract_conditions(variant_archive):
                    db.insert_condition(variant_id, condition)
                
                for location in parser.extract_genomic_locations(variant_archive):
                    db.insert_genomic_location(variant_id, location)
                
                for submitter in parser.extract_submitters(variant_archive):
                    db.insert_submitter(variant_id, submitter)
                
                processed_count += 1
                
                # Progreso cada 50 variantes
                if i % 50 == 0:
                    elapsed = time.time() - start_time
                    rate = i / elapsed
                    eta = (total_variants - i) / rate if rate > 0 else 0
                    print(f"   ⏳ {i:,}/{total_variants:,} ({i/total_variants*100:.1f}%) - "
                          f"{rate:.1f} var/seg - ETA: {eta/60:.1f}min")
                
            except Exception as e:
                error_count += 1
                if i <= 5:  # Solo mostrar errores iniciales
                    print(f"   ❌ Error en variante {i}: {e}")
        
        # Estadísticas finales
        elapsed_time = time.time() - start_time
        
        print(f"\n✅ PROCESAMIENTO COMPLETADO")
        print(f"📊 Procesadas: {processed_count:,}")
        print(f"❌ Errores: {error_count:,}")
        print(f"⏱️  Tiempo: {elapsed_time/60:.1f} minutos")
        print(f"🚀 Velocidad: {processed_count/elapsed_time:.1f} var/seg")
        
        # Verificar DB
        final_count = db.get_variant_count()
        print(f"🗄️  En DB: {final_count:,} variantes")
        
        db_size = os.path.getsize(db_file)
        print(f"📏 Tamaño DB: {db_size/1024/1024:.1f} MB")
        
        print(f"\n📁 Base de datos guardada: {db_file}")
        print(f"💡 Para consultar: python query_clinvar.py --database {db_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error crítico: {e}")
        return False
    finally:
        db.close()

def main():
    """Función principal simplificada"""
    print("🧬 ClinVar Quick Job")
    print("Procesamiento automático y rápido de XML de ClinVar\n")
    
    # Buscar archivo XML automáticamente
    xml_file = find_xml_file()
    
    if not xml_file:
        print("❌ No se encontró archivo XML en el directorio actual")
        print("💡 Asegúrate de tener un archivo .xml de ClinVar")
        return
    
    file_size = os.path.getsize(xml_file)
    print(f"📁 Archivo encontrado: {xml_file} ({file_size/1024/1024:.1f} MB)")
    
    # Determinar límite automático basado en tamaño
    if file_size > 100 * 1024 * 1024:  # > 100MB
        print("📏 Archivo grande detectado")
        print("🎯 Opciones de procesamiento:")
        print("   1. Muestra pequeña (100 variantes) - Rápido")
        print("   2. Muestra mediana (1,000 variantes) - Moderado") 
        print("   3. Muestra grande (10,000 variantes) - Lento")
        print("   4. Archivo completo - Muy lento")
        
        try:
            choice = input("\nSelecciona opción (1-4) [1]: ").strip() or "1"
            
            limits = {"1": 100, "2": 1000, "3": 10000, "4": None}
            max_variants = limits.get(choice, 100)
            
        except KeyboardInterrupt:
            print("\n❌ Cancelado por el usuario")
            return
    else:
        # Archivo pequeño, procesar todo
        max_variants = None
    
    # Ejecutar procesamiento
    success = quick_process(xml_file, max_variants)
    
    if success:
        print("\n🎉 ¡Job completado exitosamente!")
    else:
        print("\n❌ Job falló")

if __name__ == "__main__":
    main()
