#!/usr/bin/env python3
"""
ClinVar Processing Job - Ejecutor principal
<PERSON><PERSON><PERSON> que se encarga de ejecutar el job completo:
1. Lee el archivo XML de ClinVar
2. Parsea la estructura XML
3. Carga todos los datos en la base de datos SQLite

Uso:
    python execute_job.py                    # Procesar todo el archivo
    python execute_job.py --limit 1000      # Procesar solo 1000 variantes
    python execute_job.py --help            # Ver todas las opciones
"""

import os
import sys
import time
import argparse
from datetime import datetime
from database import ClinVarDatabase
from xml_parser import ClinVarXMLParser

def find_xml_file():
    """Buscar automáticamente el archivo XML de ClinVar"""
    # Buscar archivos XML en el directorio actual
    xml_files = [f for f in os.listdir(".") if f.endswith(".xml")]
    
    if not xml_files:
        return None
    
    # Priorizar archivos con nombres típicos de ClinVar
    priority_names = ['part_000.xml', 'clinvar.xml', 'ClinVar.xml']
    for name in priority_names:
        if name in xml_files:
            return name
    
    # Si no hay nombres prioritarios, tomar el más grande (probablemente el principal)
    xml_files_with_size = [(f, os.path.getsize(f)) for f in xml_files]
    largest_file = max(xml_files_with_size, key=lambda x: x[1])
    
    return largest_file[0]

def execute_clinvar_job(xml_file, db_file, max_variants=None, verbose=True):
    """
    Ejecutar el job completo de procesamiento de ClinVar
    
    Args:
        xml_file: Ruta al archivo XML de ClinVar
        db_file: Ruta donde guardar la base de datos
        max_variants: Número máximo de variantes a procesar (None = todas)
        verbose: Si mostrar información detallada
    
    Returns:
        dict: Estadísticas del procesamiento
    """
    
    def log(message):
        if verbose:
            timestamp = datetime.now().strftime("%H:%M:%S")
            print(f"[{timestamp}] {message}")
    
    # Estadísticas del job
    stats = {
        'success': False,
        'processed_count': 0,
        'error_count': 0,
        'total_variants': 0,
        'processing_time': 0,
        'db_size': 0,
        'xml_size': 0
    }
    
    start_time = time.time()
    
    try:
        log("=" * 60)
        log("EJECUTANDO JOB DE PROCESAMIENTO CLINVAR")
        log("=" * 60)
        
        # 1. Validar archivo XML
        if not os.path.exists(xml_file):
            log(f"ERROR: Archivo XML no encontrado: {xml_file}")
            return stats
        
        xml_size = os.path.getsize(xml_file)
        stats['xml_size'] = xml_size
        log(f"Archivo XML: {xml_file} ({xml_size/1024/1024:.1f} MB)")
        
        # 2. Inicializar componentes
        log("Inicializando componentes...")
        db = ClinVarDatabase(db_file)
        parser = ClinVarXMLParser()
        
        # 3. Conectar a base de datos
        log("Conectando a base de datos...")
        db.connect()
        db.initialize_database()
        
        # 4. Parsear XML
        log("Parseando estructura del XML...")
        root = parser.parse_file(xml_file)
        variation_archives = parser.get_variation_archives(root)
        total_variants = len(variation_archives)
        stats['total_variants'] = total_variants
        
        log(f"Encontradas {total_variants:,} variantes en el XML")
        
        if total_variants == 0:
            log("ERROR: No se encontraron variantes en el archivo")
            return stats
        
        # 5. Limitar variantes si es necesario
        if max_variants and max_variants < total_variants:
            variation_archives = variation_archives[:max_variants]
            total_variants = max_variants
            log(f"Limitando procesamiento a {max_variants:,} variantes")
        
        # 6. Procesar variantes
        log("Iniciando procesamiento de variantes...")
        processed_count = 0
        error_count = 0
        
        for i, variant_archive in enumerate(variation_archives, 1):
            try:
                # Extraer datos de la variante
                variant_data = parser.extract_variant_data(variant_archive)
                if not variant_data or not variant_data.get('vcv_id'):
                    error_count += 1
                    continue
                
                # Insertar variante principal
                variant_id = db.insert_variant(variant_data)
                
                # Insertar datos relacionados
                for gene in parser.extract_genes(variant_archive):
                    db.insert_gene(variant_id, gene)
                
                for condition in parser.extract_conditions(variant_archive):
                    db.insert_condition(variant_id, condition)
                
                for location in parser.extract_genomic_locations(variant_archive):
                    db.insert_genomic_location(variant_id, location)
                
                for submitter in parser.extract_submitters(variant_archive):
                    db.insert_submitter(variant_id, submitter)
                
                processed_count += 1
                
                # Mostrar progreso
                if i % 100 == 0 or i <= 10:
                    elapsed = time.time() - start_time
                    rate = i / elapsed if elapsed > 0 else 0
                    eta = (total_variants - i) / rate if rate > 0 else 0
                    percentage = (i / total_variants) * 100
                    
                    log(f"Progreso: {i:,}/{total_variants:,} ({percentage:.1f}%) - "
                        f"Velocidad: {rate:.1f} var/seg - ETA: {eta/60:.1f} min")
                
                # Log detallado para las primeras 5 variantes
                if i <= 5:
                    vcv_id = variant_data['vcv_id']
                    title = variant_data.get('title', 'Sin título')[:50]
                    log(f"   Procesada: {vcv_id} - {title}...")
                
            except Exception as e:
                error_count += 1
                if i <= 10:
                    log(f"   ERROR en variante {i}: {e}")
        
        # 7. Estadísticas finales
        processing_time = time.time() - start_time
        
        stats['processed_count'] = processed_count
        stats['error_count'] = error_count
        stats['processing_time'] = processing_time
        
        log("=" * 50)
        log("PROCESAMIENTO COMPLETADO")
        log("=" * 50)
        log(f"Variantes procesadas: {processed_count:,}")
        log(f"Errores: {error_count:,}")
        log(f"Tiempo total: {processing_time/60:.1f} minutos")
        
        if processed_count > 0:
            rate = processed_count / processing_time
            log(f"Velocidad promedio: {rate:.2f} variantes/segundo")
        
        # Verificar base de datos
        final_count = db.get_variant_count()
        log(f"Total en base de datos: {final_count:,} variantes")
        
        if os.path.exists(db_file):
            db_size = os.path.getsize(db_file)
            stats['db_size'] = db_size
            log(f"Base de datos: {db_file} ({db_size/1024/1024:.1f} MB)")
        
        stats['success'] = True
        
        # Cerrar conexiones
        db.close()
        log("Conexiones cerradas")
        
        return stats
        
    except Exception as e:
        log(f"ERROR CRÍTICO: {e}")
        return stats

def main():
    """Función principal"""
    parser = argparse.ArgumentParser(
        description='Ejecutor del job de procesamiento de ClinVar XML',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Ejemplos de uso:
  python execute_job.py                           # Procesar todo el archivo
  python execute_job.py --limit 1000             # Procesar solo 1000 variantes
  python execute_job.py --xml mi_archivo.xml     # Usar archivo específico
  python execute_job.py --database mi_db.db      # Guardar en base específica
  python execute_job.py --quiet                  # Ejecución silenciosa
        """
    )
    
    parser.add_argument('--xml', '-x', 
                       help='Archivo XML de ClinVar (se detecta automáticamente si no se especifica)')
    parser.add_argument('--database', '-d', default='clinvar.db',
                       help='Archivo de base de datos de salida (default: clinvar.db)')
    parser.add_argument('--limit', '-l', type=int,
                       help='Número máximo de variantes a procesar')
    parser.add_argument('--quiet', '-q', action='store_true',
                       help='Ejecución silenciosa (solo mostrar resultado final)')
    
    args = parser.parse_args()
    
    # Buscar archivo XML si no se especifica
    xml_file = args.xml
    if not xml_file:
        xml_file = find_xml_file()
        if not xml_file:
            print("ERROR: No se encontró archivo XML en el directorio actual")
            print("Especifica un archivo con --xml o coloca un archivo .xml en el directorio")
            sys.exit(1)
    
    # Ejecutar job
    print(f"Ejecutando job de ClinVar...")
    print(f"XML: {xml_file}")
    print(f"Base de datos: {args.database}")
    if args.limit:
        print(f"Límite: {args.limit:,} variantes")
    print()
    
    stats = execute_clinvar_job(
        xml_file=xml_file,
        db_file=args.database,
        max_variants=args.limit,
        verbose=not args.quiet
    )
    
    # Mostrar resultado final
    print("\n" + "=" * 60)
    if stats['success']:
        print("*** JOB COMPLETADO EXITOSAMENTE ***")
        print(f"Variantes procesadas: {stats['processed_count']:,}")
        print(f"Tiempo de procesamiento: {stats['processing_time']/60:.1f} minutos")
        print(f"Base de datos: {args.database} ({stats['db_size']/1024/1024:.1f} MB)")
        print(f"\nPara consultar los datos:")
        print(f"  python query_clinvar.py --database {args.database}")
        sys.exit(0)
    else:
        print("*** JOB FALLÓ ***")
        print("Revisa los mensajes de error anteriores")
        sys.exit(1)

if __name__ == "__main__":
    main()
