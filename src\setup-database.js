import Database from './database.js';

async function setupDatabase() {
  console.log('Configurando base de datos ClinVar...');
  
  const db = new Database();
  
  try {
    await db.initialize();
    console.log('✅ Base de datos configurada correctamente');
    
    // Verificar que las tablas se crearon
    const tables = await db.all(`
      SELECT name FROM sqlite_master 
      WHERE type='table' AND name NOT LIKE 'sqlite_%'
      ORDER BY name
    `);
    
    console.log('\n📋 Tablas creadas:');
    tables.forEach(table => {
      console.log(`  - ${table.name}`);
    });
    
  } catch (error) {
    console.error('❌ Error configurando la base de datos:', error);
  } finally {
    db.close();
  }
}

setupDatabase();
