import Database from './database.js';
import ClinVarProcessor from './process-xml.js';

class ClinVarApp {
  constructor() {
    this.db = new Database();
  }

  async start() {
    console.log('🧬 ClinVar XML Processor');
    console.log('========================\n');

    try {
      // Inicializar base de datos
      await this.db.initialize();
      
      // Mostrar estadísticas actuales
      await this.showCurrentStats();
      
      // Preguntar al usuario qué hacer
      console.log('\nOpciones disponibles:');
      console.log('1. Procesar archivo XML (part_000.xml)');
      console.log('2. Ver estadísticas de la base de datos');
      console.log('3. Ver últimas variantes procesadas');
      console.log('4. Salir');
      
    } catch (error) {
      console.error('Error iniciando la aplicación:', error);
    }
  }

  async showCurrentStats() {
    const count = await this.db.getVariantCount();
    console.log(`📊 Variantes en la base de datos: ${count}`);
  }

  async processXML() {
    const processor = new ClinVarProcessor();
    try {
      await processor.initialize();
      await processor.processXMLFile('./part_000.xml');
      await processor.showStatistics();
    } finally {
      await processor.close();
    }
  }

  async showVariants(limit = 10) {
    console.log(`\n📋 Últimas ${limit} variantes:`);
    const variants = await this.db.getVariants(limit);
    
    if (variants.length === 0) {
      console.log('No hay variantes en la base de datos');
      return;
    }

    variants.forEach((variant, index) => {
      console.log(`\n${index + 1}. ${variant.vcv_id}`);
      console.log(`   Título: ${variant.title}`);
      console.log(`   Significancia: ${variant.clinical_significance || 'No especificada'}`);
      console.log(`   Estado de revisión: ${variant.review_status || 'No especificado'}`);
      console.log(`   Última actualización: ${variant.date_last_updated || 'No especificada'}`);
    });
  }

  async close() {
    this.db.close();
  }
}

// Función principal para demostración
async function demo() {
  const app = new ClinVarApp();
  
  try {
    await app.start();
    
    console.log('\n🚀 Ejecutando procesamiento automático del XML...\n');
    await app.processXML();
    
    console.log('\n📋 Mostrando últimas variantes procesadas...\n');
    await app.showVariants(5);
    
  } catch (error) {
    console.error('Error en la demostración:', error);
  } finally {
    await app.close();
  }
}

// Ejecutar demostración
demo();
