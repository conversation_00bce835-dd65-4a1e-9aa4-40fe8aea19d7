#!/usr/bin/env python3
"""
Resumen visual de la base de datos ClinVar abierta
"""

import sqlite3
import os
from datetime import datetime

def create_visual_summary():
    """Crear un resumen visual completo de la base de datos"""
    db_path = "clinvar_sample.db"
    
    if not os.path.exists(db_path):
        print(f"❌ Base de datos no encontrada: {db_path}")
        return
    
    print("🧬 RESUMEN COMPLETO DE LA BASE DE DATOS CLINVAR")
    print("=" * 60)
    
    # Información del archivo
    file_size = os.path.getsize(db_path)
    file_time = datetime.fromtimestamp(os.path.getmtime(db_path))
    
    print(f"📁 Archivo: {db_path}")
    print(f"📏 Tamaño: {file_size:,} bytes ({file_size/1024:.1f} KB)")
    print(f"📅 Última modificación: {file_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Estadísticas principales
        print(f"\n📊 ESTADÍSTICAS PRINCIPALES")
        print("-" * 30)
        
        cursor.execute("SELECT COUNT(*) FROM variants")
        variant_count = cursor.fetchone()[0]
        print(f"🧬 Variantes totales: {variant_count:,}")
        
        cursor.execute("SELECT COUNT(DISTINCT gene_symbol) FROM genes WHERE gene_symbol IS NOT NULL")
        unique_genes = cursor.fetchone()[0]
        print(f"🧬 Genes únicos: {unique_genes:,}")
        
        cursor.execute("SELECT COUNT(DISTINCT condition_name) FROM conditions WHERE condition_name IS NOT NULL")
        unique_conditions = cursor.fetchone()[0]
        print(f"🏥 Condiciones únicas: {unique_conditions:,}")
        
        cursor.execute("SELECT COUNT(DISTINCT submitter_name) FROM submitters WHERE submitter_name IS NOT NULL")
        unique_submitters = cursor.fetchone()[0]
        print(f"👥 Submitters únicos: {unique_submitters:,}")
        
        cursor.execute("SELECT COUNT(DISTINCT chromosome) FROM genomic_locations WHERE chromosome IS NOT NULL")
        unique_chromosomes = cursor.fetchone()[0]
        print(f"📍 Cromosomas representados: {unique_chromosomes:,}")
        
        # Distribución temporal
        print(f"\n📅 DISTRIBUCIÓN TEMPORAL")
        print("-" * 25)
        
        cursor.execute("""
            SELECT substr(date_created, 1, 4) as year, COUNT(*) as count
            FROM variants
            WHERE date_created IS NOT NULL
            GROUP BY year
            ORDER BY year
        """)
        
        years = cursor.fetchall()
        print("Variantes por año de creación:")
        for year, count in years:
            bar = "█" * min(int(count/2), 20)  # Escala visual
            print(f"   {year}: {count:2d} {bar}")
        
        # Top genes
        print(f"\n🧬 TOP 10 GENES MÁS FRECUENTES")
        print("-" * 35)
        
        cursor.execute("""
            SELECT gene_symbol, COUNT(DISTINCT variant_id) as variant_count
            FROM genes
            WHERE gene_symbol IS NOT NULL
            GROUP BY gene_symbol
            ORDER BY variant_count DESC
            LIMIT 10
        """)
        
        top_genes = cursor.fetchall()
        max_count = max([count for _, count in top_genes]) if top_genes else 1
        
        for i, (gene, count) in enumerate(top_genes, 1):
            bar_length = int((count / max_count) * 20)
            bar = "█" * bar_length
            print(f"   {i:2d}. {gene:8s}: {count:2d} {bar}")
        
        # Tipos de variantes
        print(f"\n🔬 TIPOS DE VARIANTES")
        print("-" * 20)
        
        cursor.execute("""
            SELECT variation_type, COUNT(*) as count
            FROM variants
            WHERE variation_type IS NOT NULL
            GROUP BY variation_type
            ORDER BY count DESC
        """)
        
        var_types = cursor.fetchall()
        max_count = max([count for _, count in var_types]) if var_types else 1
        
        for var_type, count in var_types:
            bar_length = int((count / max_count) * 20)
            bar = "█" * bar_length
            percentage = (count / variant_count) * 100
            print(f"   {var_type:25s}: {count:2d} ({percentage:4.1f}%) {bar}")
        
        # Distribución cromosómica
        print(f"\n📍 DISTRIBUCIÓN CROMOSÓMICA (TOP 10)")
        print("-" * 40)
        
        cursor.execute("""
            SELECT chromosome, COUNT(*) as count
            FROM genomic_locations
            WHERE chromosome IS NOT NULL
            GROUP BY chromosome
            ORDER BY count DESC
            LIMIT 10
        """)
        
        chromosomes = cursor.fetchall()
        max_count = max([count for _, count in chromosomes]) if chromosomes else 1
        
        for chr_name, count in chromosomes:
            bar_length = int((count / max_count) * 20)
            bar = "█" * bar_length
            print(f"   Chr{chr_name:2s}: {count:3d} {bar}")
        
        # Submitters más activos
        print(f"\n👥 TOP 5 SUBMITTERS MÁS ACTIVOS")
        print("-" * 35)
        
        cursor.execute("""
            SELECT submitter_name, COUNT(*) as submissions
            FROM submitters
            WHERE submitter_name IS NOT NULL
            GROUP BY submitter_name
            ORDER BY submissions DESC
            LIMIT 5
        """)
        
        top_submitters = cursor.fetchall()
        for i, (submitter, count) in enumerate(top_submitters, 1):
            submitter_short = submitter[:30] + "..." if len(submitter) > 30 else submitter
            print(f"   {i}. {submitter_short:33s}: {count:2d} envíos")
        
        # Condiciones más comunes (excluyendo genéricas)
        print(f"\n🏥 TOP 5 CONDICIONES ESPECÍFICAS")
        print("-" * 35)
        
        cursor.execute("""
            SELECT condition_name, COUNT(*) as count
            FROM conditions
            WHERE condition_name IS NOT NULL 
            AND condition_name NOT IN ('not provided', 'none provided', 'not specified')
            AND LENGTH(condition_name) > 10
            GROUP BY condition_name
            ORDER BY count DESC
            LIMIT 5
        """)
        
        top_conditions = cursor.fetchall()
        for i, (condition, count) in enumerate(top_conditions, 1):
            condition_short = condition[:40] + "..." if len(condition) > 40 else condition
            print(f"   {i}. {condition_short:43s}: {count:2d}")
        
        # Ejemplos de variantes interesantes
        print(f"\n🌟 EJEMPLOS DE VARIANTES INTERESANTES")
        print("-" * 40)
        
        # Variante con más genes
        cursor.execute("""
            SELECT v.vcv_id, v.title, COUNT(g.id) as gene_count
            FROM variants v
            LEFT JOIN genes g ON v.id = g.variant_id
            GROUP BY v.id
            ORDER BY gene_count DESC
            LIMIT 1
        """)
        
        top_variant = cursor.fetchone()
        if top_variant:
            vcv_id, title, gene_count = top_variant
            title_short = title[:50] + "..." if len(title) > 50 else title
            print(f"   🧬 Más genes asociados: {vcv_id}")
            print(f"      {title_short}")
            print(f"      Genes: {gene_count}")
        
        # Variante más reciente
        cursor.execute("""
            SELECT vcv_id, title, date_last_updated
            FROM variants
            WHERE date_last_updated IS NOT NULL
            ORDER BY date_last_updated DESC
            LIMIT 1
        """)
        
        recent_variant = cursor.fetchone()
        if recent_variant:
            vcv_id, title, date_updated = recent_variant
            title_short = title[:50] + "..." if len(title) > 50 else title
            print(f"\n   📅 Más recientemente actualizada: {vcv_id}")
            print(f"      {title_short}")
            print(f"      Actualizada: {date_updated}")
        
        # Rango genómico más grande
        cursor.execute("""
            SELECT v.vcv_id, v.title, gl.chromosome, 
                   (gl.stop_position - gl.start_position) as range_size
            FROM variants v
            JOIN genomic_locations gl ON v.id = gl.variant_id
            WHERE gl.start_position IS NOT NULL AND gl.stop_position IS NOT NULL
            AND gl.stop_position > gl.start_position
            ORDER BY range_size DESC
            LIMIT 1
        """)
        
        large_variant = cursor.fetchone()
        if large_variant:
            vcv_id, title, chromosome, range_size = large_variant
            title_short = title[:50] + "..." if len(title) > 50 else title
            print(f"\n   📏 Rango genómico más grande: {vcv_id}")
            print(f"      {title_short}")
            print(f"      Chr{chromosome}: {range_size:,} bp")
        
        print(f"\n" + "=" * 60)
        print(f"✅ Base de datos ClinVar completamente explorada")
        print(f"💡 Usa 'python query_clinvar.py' para consultas específicas")
        
    except Exception as e:
        print(f"❌ Error generando resumen: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    create_visual_summary()
